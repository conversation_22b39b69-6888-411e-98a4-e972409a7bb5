from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from typing import Dict
from loguru import logger

from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.ctcase_build.models import ConfirmFormatCtCase


def process_combined_data(combined_data: Dict[str, Dict[str, Dict]], config: str) -> Dict:

    """处理从Excel提取的组合数据"""
    if not combined_data:
        logger.warning("接收到空的组合数据")
        return {}
        
    # 创建一个聊天提示模板，用于生成变更摘要
    template = ChatPromptTemplate(
        [
            ("user", config)
        ],
        template_format="mustache",
    )
    
    invoke_data = {
        "input_data": combined_data,
    }

    result: ConfirmFormatCtCase = get_ai_message_with_structured_output(
        template,
        invoke_data,
        ConfirmFormatCtCase,
        llm_model=None
    )
    return result.data