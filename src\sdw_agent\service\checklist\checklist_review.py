
import asyncio
from openpyxl import load_workbook
from openpyxl.styles import Border, Side, Font, PatternFill
from loguru import logger
from typing import Dict, List, Any, Optional
from scipy.spatial.distance import cosine
from sdw_agent.llm.openai_qwen import OpenAIQwen
from sdw_agent.util.excel_util import ReadExcel
from sdw_agent.service.checklist.prompt import select_checkitems_prompt_rule
from sdw_agent.service.checklist.demonstration import demonstrations
from sdw_agent.llm.model import openai_embeddings
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from sdw_agent.util.excel.core import CellStyle, CellRange, OpenpyxlEngine


# 变更点 {'対応イベント': 'CV', 'ARチケットNO': 'MET19PFV3-21732', 'ARチケットNOリンク': 'MET19PFV3-21732', 'ARチケットタイトル': '【顧客要求_変更】MET-G_CSTMLST-CSTD-A0-03-A-C0', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC R_第0階層⇒第一階層へ移動 1500Wコンセント,2400Wコンセント,7200Wコンセント', '概要': '第0階層⇒第一階層へ移動\n1500Wコンセント,2400Wコンセント,7200Wコンセント', '要件チケットNO': '〇', 'R核': '1500Wコンセント&2400Wコンセント&7200Wコンセント功能需求.md'}
# 检查项 {'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇'}
# 测试用例 {'示例': '1', '模块名称': '時計設定\n【#450】', '确认点': '第0阶层-设定有无', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送信号AVN1S30(ID:31A)', '画面显示': '不显示“时钟设定”', 'sheet_name': 'カスタマイズ項目一覧'}


@register_workflow("checklist_review")
class ChecklistReviewWorkflow(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.rule_dict = None
        self.llm = OpenAIQwen(base_url="http://172.30.19.113:11400/v1", api_key="sk-xxx", model="qwen3")

    def _config_rule(self, rule_file=None):
        rule_dict = {}
        if rule_file is not None:
            wb = load_workbook(filename=rule_file)
            ws = wb[self.config["rule_sheet_name"]]
            category = None
            for i, row in enumerate(ws.iter_rows(values_only=True)):
                if i:
                    changepoint_category, changepoint_keyword, checkitem, _, checkitem_keyword = row
                    if changepoint_category is not None:
                        rule_dict[changepoint_category] = {'changepoint_keyword': [], 'checkitem': []}
                        category = changepoint_category
                    if category is not None:
                        if changepoint_keyword is not None:
                            rule_dict[category]['changepoint_keyword'].append(changepoint_keyword)
                        if checkitem is not None:
                            rule_dict[category]['checkitem'].append({'value': checkitem, 'checkitem_keyword': []})
                            if checkitem_keyword is not None:
                                rule_dict[category]['checkitem'][-1]['checkitem_keyword'] += checkitem_keyword.split('、')
        return rule_dict


    async def execute(self, changepoint_file_path: str, testcase_file_path: str, checklist_file_path: str) -> str:
        self.rule_dict = self._config_rule(rule_file=checklist_file_path)
        logger.info(self.config)
        logger.info(self.rule_dict)
        changepoint_res, testcase_res, checklist_res = self.get_changepoints_testcases_checklists(changepoint_file_path, testcase_file_path, checklist_file_path)
        selected_checkitem_results = await self.select_checkitems_by_changepoints_rule(changepoint_res=changepoint_res, checklist_res=checklist_res)
        selected_testcase_results = self.match_testcase4checkitem(selected_checkitems=selected_checkitem_results, testcase_res=testcase_res)
        save_path = self.writeback2excel(selected_testcases=selected_testcase_results, checklist_file=checklist_file_path)
        return save_path

    def get_changepoints_testcases_checklists(self, changepoint_file: str, testcase_file: str, checklist_file: str) -> tuple[list, list, list]:
        """
        解析文件，获得结构化数据
        """
        changepoint_save_path, changepoint_res = ReadExcel.parse_excel(path_type="changepoints", excel_path=changepoint_file)
        testcase_save_path, testcase_res = ReadExcel.parse_excel(path_type="testcases", excel_path=testcase_file)
        checklist_save_path, checklist_res = ReadExcel.parse_excel(path_type="checklists", excel_path=checklist_file)
        changepoint_res = list(filter(lambda x: self.config['changepoint_id'] in x.keys() and self.config['changepoint_description'] in x.keys(), changepoint_res))
        testcase_res = list(filter(lambda x: 'sheet_name' in x.keys() and '示例' in x.keys(), testcase_res))
        return changepoint_res, testcase_res, checklist_res

    # async def select_checkitems_by_changepoints(self, changepoint_res: list, checklist_res: list) -> list:
    #     """
    #     为每一个变更点选择对应的检查项集合
    #     """
    #     results = []
    #     checkitems = '\n'.join([f"{i}.{checklist_res[i]['类型']}：{checklist_res[i][self.config['checkitem_type']]}" for i in range(len(checklist_res))])
    #     prompts = [select_checkitems_prompt.format(input=changepoint_res[i][self.config['changepoint_description']], checkitems=checkitems) for i in range(len(changepoint_res))]
    #     for i, prompt in enumerate(prompts):
    #         try:
    #             response = await self.llm.generate_text(system_message=prompt, temperature=0.1, top_p=0.3)
    #             result = self.llm.parse_content2json(responses=self.llm.parse_content_from_response(responses=[response], start_token='[', end_token=']'), default='list')[0]
    #         except Exception as e:
    #             logger.error(e)
    #             result = []
    #         result = {self.config['changepoint_id']: changepoint_res[i][self.config['changepoint_id']], self.config['changepoint_description']: changepoint_res[i][self.config['changepoint_description']],
    #                   'checkitems': [{**checklist_res[j], **{'是否相关': c['是否相关'], '原因': c['原因']}} for j, c in enumerate(result) if j < len(checklist_res)]}
    #         results.append(result)
    #         logger.info(result)
    #     return results
    #
    # async def select_checkitems_by_changepoints_ICL(self, changepoint_res: list, checklist_res: list) -> list:
    #     """
    #     为每一个变更点选择对应的检查项集合
    #     """
    #     results = []
    #     top_num = 1
    #     vectors = openai_embeddings.embed_documents(texts=[demonstration['变更点'] for demonstration in demonstrations])
    #     checkitems = '\n'.join([f"{i}.{checklist_res[i]['类型']}：{checklist_res[i][self.config['checkitem_type']]}" for i in range(len(checklist_res))])
    #     for i, changepoint in enumerate(changepoint_res):
    #         vector = openai_embeddings.embed_query(text=changepoint[self.config['changepoint_description']])
    #         similarity_dicts = []
    #         for j in range(len(vectors)):
    #             cosine_distance = cosine(vector, vectors[j])
    #             cosine_similarity = 1 - cosine_distance
    #             similarity_dicts.append({'description': demonstrations[j]['变更点'], 'similarity': cosine_similarity, 'index': j})
    #         similarity_dicts = sorted(similarity_dicts, key=lambda x: x['similarity'], reverse=True)
    #         example = "\n".join([f"输入：{demonstrations[similarity_dicts[k]['index']]['变更点']}\n输出：{json.dumps(demonstrations[similarity_dicts[k]['index']]['related_checkitems'], ensure_ascii=False)}\n" for k in range(top_num)])
    #         prompt = select_checkitems_prompt_ICL.format(checkitems=checkitems, examples=example, input=changepoint[self.config['changepoint_description']])
    #         try:
    #             response = await self.llm.generate_text(system_message=prompt, temperature=0.1, top_p=0.3)
    #             result = self.llm.parse_content2json(responses=self.llm.parse_content_from_response(responses=[response], start_token='[', end_token=']'), default='list')[0]
    #         except Exception as e:
    #             logger.error(e)
    #             result = []
    #         result = {self.config['changepoint_id']: changepoint_res[i][self.config['changepoint_id']], self.config['changepoint_description']: changepoint_res[i][self.config['changepoint_description']],
    #                   'checkitems': [{**checklist_res[j], **{'是否相关': c['是否相关'], '原因': c['原因']}} for j, c in enumerate(result) if j < len(checklist_res)]}
    #         results.append(result)
    #         logger.info(result)
    #     return results

    async def select_checkitems_by_changepoints_rule(self, changepoint_res, checklist_res):
        """
        为每一个变更点选择对应的检查项集合
        """
        results = []
        change_category_str = '\n'.join([f"{index+1}.{key}\t# {str(self.rule_dict[key]['changepoint_keyword'])}" for index, key in enumerate(self.rule_dict.keys())])
        prompts = [select_checkitems_prompt_rule.format(changepoint_category=change_category_str, input=changepoint_res[i][self.config['changepoint_description']]) for i in range(len(changepoint_res))]
        for index, prompt in enumerate(prompts):
            try:
                response = await self.llm.generate_text(system_message=prompt, temperature=0.1, top_p=0.3)
                result = self.llm.parse_content2json(responses=self.llm.parse_content_from_response(responses=[response], start_token='{', end_token='}'), default='dict')[0]
            except Exception as e:
                logger.error(e)
                result = {}
            checkitems = []
            if '变更点类别' in result.keys():
                for checkitem in self.rule_dict[result['变更点类别']]['checkitem']:
                    try:
                        checkitem_list = [checklist_res[k][self.config['checkitem_type']].split('-')[-1] for k in range(len(checklist_res))]
                        pos = checkitem_list.index(checkitem['value'])
                        checkitems.append({**checklist_res[pos], **{'是否相关': '是', '原因': ''}})
                    except Exception as e:
                        pass
            result = {self.config['changepoint_id']: changepoint_res[index][self.config['changepoint_id']], self.config['changepoint_description']: changepoint_res[index][self.config['changepoint_description']], '变更点类别': result['变更点类别'], 'checkitems': checkitems[:]}
            results.append(result)
            logger.info(result)
        return results

    def match_testcase4checkitem(self, selected_checkitems: list, testcase_res: list) -> list:
        """
        为每一对变更点-检查项匹配对应的测试用例
        """
        results = selected_checkitems[:]
        testcase_texts = []
        for testcase in testcase_res:
            if self.config['confirm_point'] in testcase.keys():
                text = f"{testcase[self.config['confirm_point']]}，{testcase.get(self.config['precondition'], '')}，{testcase.get(self.config['screen_display'], '')}"
                testcase_texts.append(text)
        vectors = openai_embeddings.embed_documents(texts=testcase_texts)
        for i, point in enumerate(selected_checkitems):
            for j, checkitem in enumerate(point['checkitems']):
                results[i]['checkitems'][j]['testcases'] = []
                results[i]['checkitems'][j]['reason'] = ""
                if checkitem['是否相关'] == '是':
                    checkitem_subname = checkitem[self.config['checkitem_type']].split('-')[-1]
                    index = [self.rule_dict[results[i]['变更点类别']]['checkitem'][k]['value'] for k in range(len(self.rule_dict[results[i]['变更点类别']]['checkitem']))].index(checkitem_subname)
                    if results[i]['变更点类别'] in self.rule_dict.keys() and self.rule_dict[results[i]['变更点类别']]['checkitem'][index]['checkitem_keyword']:
                        keywords = self.rule_dict[results[i]['变更点类别']]['checkitem'][index]['checkitem_keyword'] if index >= 0 else []
                        for keyword in keywords:
                            results[i]['checkitems'][j]['testcases'] += [testcase_res[k] for k in range(len(testcase_res)) if keyword in testcase_texts[k]]
                        if results[i]['checkitems'][j]['testcases']:
                            results[i]['checkitems'][j]['reason'] = f"存在符合关键字{'，'.join(self.rule_dict[results[i]['变更点类别']]['checkitem'][index]['checkitem_keyword'])}的测试用例"
                        else:
                            results[i]['checkitems'][j]['reason'] += f"没有符合关键字{'，'.join(self.rule_dict[results[i]['变更点类别']]['checkitem'][index]['checkitem_keyword'])}的测试用例"
                    else:
                        text = checkitem[self.config['checkitem_type']]
                        vector = openai_embeddings.embed_query(text=text)
                        max_value = -1
                        for k, testcase_vector in enumerate(vectors):
                            cosine_distance = cosine(vector, testcase_vector)
                            cosine_similarity = 1 - cosine_distance
                            max_value = max(max_value, cosine_similarity)
                            if cosine_similarity >= self.config['threshold']:
                                new_case = testcase_res[k]
                                results[i]['checkitems'][j]['testcases'].append(new_case)
                                results[i]['checkitems'][j]['reason'] += f"{new_case['sheet_name']}_{new_case['示例']}的语义匹配度为{cosine_similarity}；"
                        if results[i]['checkitems'][j]['reason'] == "":
                            results[i]['checkitems'][j]['reason'] = f"所有的测试用例中语义匹配度最高为{max_value}"
            print(results[i]['checkitems'])
        return results

    def writeback2excel(self, selected_testcases: list, checklist_file: str) -> str:
        """
        调整输出文件的格式并写入结果
        """
        save_path = checklist_file.split('.xlsx')[0] + '_output.xlsx'
        writer = OpenpyxlEngine(file_path=checklist_file)
        wb = load_workbook(filename=checklist_file)
        other_sheets = wb.sheetnames
        other_sheets.remove(self.config['template']['sheet_name'])
        for sheet in other_sheets:
            wb.remove(wb[sheet])
        ws = wb[self.config['template']['sheet_name']]
        ws.unmerge_cells(range_string=self.config['template']['current_title_range'])
        ws.merge_cells(range_string=self.config['template']['expect_title_range'])
        if self.config['template']['require_delete']:
            for i in range(self.config['template']['delete_num']):
                ws.delete_cols(idx=self.config['template']['start_column'])
        style1 = CellStyle(font_name='Calibri', font_size=12, font_bold=True, alignment_horizontal='center', border_style='thin', bg_color="717171")
        style2 = CellStyle(font_name='Calibri', font_size=12, font_bold=False, alignment_horizontal='center', border_style='thin')
        style3 = CellStyle(font_name='Calibri', font_size=12, font_bold=False, alignment_horizontal='left', border_style='thin')
        require_name = self.config['template']['require_name']
        reason_name = self.config['template']['reason_name']
        check_name = self.config['template']['check_name']
        remark_name = self.config['template']['remark_name']
        start_row, start_column, offset = self.config['template']['start_row'], self.config['template']['start_column'], self.config['template']['offset']
        for i, point in enumerate(selected_testcases):
            changepoint_name = selected_testcases[i][self.config['changepoint_id']]
            ws.cell(row=start_row-1, column=start_column+i*offset).value = changepoint_name
            writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=start_row-1, col=start_column+i*offset, style=style1)
            ws.cell(row=start_row, column=start_column+i*offset).value = require_name
            writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=start_row, col=start_column+i*offset, style=style1)
            ws.cell(row=start_row, column=start_column+i*offset+1).value = check_name
            writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=start_row, col=start_column+i*offset+1, style=style1)
            ws.cell(row=start_row, column=start_column+i*offset+2).value = reason_name
            writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=start_row, col=start_column+i*offset+2, style=style1)
            ws.cell(row=start_row, column=start_column+i*offset+3).value = remark_name
            writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=start_row, col=start_column+i*offset+3, style=style1)
            for j in range(start_row+1, ws.max_row):
                if ws.cell(row=j, column=start_column-1).value is None:
                    continue
                element_name = ws.cell(row=j, column=start_column-1).value
                search_list = [checkitem[self.config['checkitem_type']].split('-')[-1] for checkitem in point['checkitems']]
                index = -1
                try:
                    index = search_list.index(element_name)
                except Exception:
                    pass
                if index >= 0:
                    if point['checkitems'][index]['是否相关'] == '是':
                        ws.cell(row=j, column=start_column+i*offset).value = '〇'
                    else:
                        ws.cell(row=j, column=start_column + i * offset).value = ''
                    testcase_list = point['checkitems'][index]['testcases']
                    if testcase_list:
                        ws.cell(row=j, column=start_column+i*offset+1).value = '√'
                        id_dict = {}
                        for testcase in testcase_list:
                            if testcase['sheet_name'] in id_dict.keys():
                                id_dict[testcase['sheet_name']].append(testcase['示例'])
                            else:
                                id_dict[testcase['sheet_name']] = [testcase['示例']]
                        strs = '；'.join([key+'_'+'，'.join(id_dict[key]) for key in id_dict.keys()])
                        ws.cell(row=j, column=start_column+i*offset+3).value = strs
                    elif point['checkitems'][index]['是否相关'] == '是':
                        ws.cell(row=j, column=start_column+i*offset+1).value = '×'
                    ws.cell(row=j, column=start_column+i*offset+2).value = point['checkitems'][index]['reason']
                else:
                    ws.cell(row=j, column=start_column+i*offset).value = ''
                writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=j, col=start_column+i*offset, style=style2)
                writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=j, col=start_column+i*offset+1, style=style2)
                writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=j, col=start_column+i*offset+2, style=style3)
                writer.set_cell_style(sheet_name=self.config['template']['sheet_name'], row=j, col=start_column+i*offset+3, style=style3)
        wb.save(filename=save_path)
        return save_path




if __name__ == '__main__':
    reviewer = ChecklistReviewWorkflow()
    changepoint_res, testcase_res, checklist_res = reviewer.get_changepoints_testcases_checklists('changepoint.xlsx', 'testcase.xlsx', 'checklist_new.xlsx')
    selected_checkitem_results = asyncio.run(reviewer.select_checkitems_by_changepoints_rule(changepoint_res=changepoint_res[:2], checklist_res=checklist_res))
    print(selected_checkitem_results)
    selected_testcase_results = reviewer.match_testcase4checkitem(selected_checkitems=selected_checkitem_results, testcase_res=testcase_res)
    reviewer.writeback2excel(selected_testcases=selected_testcase_results, checklist_file='checklist_new.xlsx')