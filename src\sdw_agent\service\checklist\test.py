
import requests


if __name__ == '__main__':
    dir_path = 'D:\\projects\\DEV_Agent\\src\\sdw_agent\\service\\checklist\\'
    input_data = {'changepoint_file_path': dir_path+'changepoint.xlsx', 'testcase_file_path': dir_path+'testcase.xlsx', 'checklist_file_path': dir_path+'checklist_new.xlsx'}
    output_data = requests.post('http://127.0.0.1:8001/api/sdw/checklist_review', json=input_data).text
    print(output_data)
