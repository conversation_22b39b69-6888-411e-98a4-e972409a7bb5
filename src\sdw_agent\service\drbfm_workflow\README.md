# DRBFM工作流

## 概述

DRBFM (Design Review Based on Failure Mode) 工作流是基于故障模式的设计评审工作流，用于系统性地分析设计变更可能带来的风险和故障模式。

## 工作流步骤

DRBFM工作流包含7个主要步骤：

### 1. 要件和SCL分析
- **功能**: 分析项目要件和SCL（Software Configuration List）
- **输入**: 要件文件、SCL文件、分析范围
- **输出**: 分析结果、要件摘要、SCL摘要
- **API端点**: `POST /api/sdw/drbfm/analysis`

### 2. 变更概要写入DRBFM
- **功能**: 将变更概要信息写入DRBFM文档
- **输入**: DRBFM文件路径、变更概要、变更理由、变更范围
- **输出**: 更新后的DRBFM文件
- **API端点**: `POST /api/sdw/drbfm/summary`

### 3. Block图生成并写入DRBFM
- **功能**: 生成系统Block图并写入DRBFM文档
- **输入**: 系统组件列表、组件关系、需要高亮的组件
- **输出**: Block图文件、更新后的DRBFM文件
- **API端点**: `POST /api/sdw/drbfm/block_diagram`

### 4. 变更点、变化点比较作成，写入DRBFM
- **功能**: 分析变更前后的差异，生成变更点比较表
- **输入**: 变更前状态、变更后状态、变更点列表、影响分析
- **输出**: 变更点比较表、影响分析结果
- **API端点**: `POST /api/sdw/drbfm/change_point`

### 5. 担心点抽出表作成，写入DRBFM
- **功能**: 识别和分析潜在的担心点，生成担心点抽出表
- **输入**: 担心点分类、风险因子、严重程度等级
- **输出**: 担心点抽出表、风险评估结果
- **API端点**: `POST /api/sdw/drbfm/concern`

### 6. FTA作成，写入DRBFM
- **功能**: 进行故障树分析（Fault Tree Analysis），生成FTA图
- **输入**: 故障模式列表、根本原因、概率数据
- **输出**: FTA图文件、故障分析结果
- **API端点**: `POST /api/sdw/drbfm/fta`

### 7. DRBFM sheet页作成
- **功能**: 整合所有DRBFM内容，生成最终的DRBFM sheet页
- **输入**: 项目信息、评审成员、评审日期
- **输出**: 完整的DRBFM文档
- **API端点**: `POST /api/sdw/drbfm/sheet`

## 文件结构

```
drbfm_workflow/
├── __init__.py              # 模块初始化文件
├── drbfm_workflow.py        # 主工作流实现
├── models.py                # 数据模型定义
├── config.yaml              # 配置文件
└── README.md                # 说明文档
```

## 使用方法

### 1. 导入模块

```python
from sdw_agent.service.drbfm_workflow import (
    DrbfmWorkflow,
    DrbfmAnalysisRequest,
    DrbfmSummaryRequest,
    # ... 其他模型
)
```

### 2. 创建工作流实例

```python
workflow = DrbfmWorkflow()
```

### 3. 执行工作流步骤

```python
# 步骤1: 要件和SCL分析
analysis_request = DrbfmAnalysisRequest(
    project_id="PROJECT_001",
    requirements_file_path="/path/to/requirements.xlsx",
    scl_file_path="/path/to/scl.xlsx",
    analysis_scope="全系统分析"
)
result = workflow.execute_analysis(analysis_request)

# 步骤2: 变更概要写入
summary_request = DrbfmSummaryRequest(
    drbfm_file_path="/path/to/drbfm.xlsx",
    change_summary="系统升级变更",
    change_reason="性能优化",
    change_scope="核心模块"
)
result = workflow.execute_summary(summary_request)

# ... 其他步骤类似
```

## API使用示例

### 要件和SCL分析

```bash
curl -X POST "http://localhost:8000/api/sdw/drbfm/analysis" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "PROJECT_001",
    "requirements_file_path": "/path/to/requirements.xlsx",
    "scl_file_path": "/path/to/scl.xlsx",
    "analysis_scope": "全系统分析"
  }'
```

### 变更概要写入

```bash
curl -X POST "http://localhost:8000/api/sdw/drbfm/summary" \
  -H "Content-Type: application/json" \
  -d '{
    "drbfm_file_path": "/path/to/drbfm.xlsx",
    "change_summary": "系统升级变更",
    "change_reason": "性能优化",
    "change_scope": "核心模块"
  }'
```

## 配置说明

工作流的配置在 `config.yaml` 文件中定义，包括：

- **基本信息**: 工作流名称、版本、作者等
- **工作流常量**: 超时时间、重试次数、支持的文件格式等
- **模块特定配置**: 各个步骤的具体配置参数
- **消息常量**: 成功、错误、警告消息模板
- **输出配置**: 输出目录、文件命名规则等
- **性能配置**: 并发、内存、缓存配置
- **验证配置**: 输入输出验证规则

## 注意事项

1. **文件路径**: 确保所有输入文件路径正确且文件存在
2. **权限**: 确保有足够的权限读取输入文件和写入输出文件
3. **格式**: 支持的文件格式包括 .xlsx、.xls、.drawio、.pdf
4. **内存**: 大文件处理可能需要较多内存，注意配置合理的内存限制
5. **超时**: 复杂分析可能需要较长时间，可根据需要调整超时配置

## 扩展开发

如需扩展DRBFM工作流功能：

1. 在 `models.py` 中添加新的数据模型
2. 在 `drbfm_workflow.py` 中实现新的执行方法
3. 在 `drbfm_workflow_api.py` 中添加对应的API端点
4. 更新 `config.yaml` 中的相关配置
5. 更新 `__init__.py` 中的导出列表

## 故障排除

常见问题及解决方案：

1. **文件未找到**: 检查文件路径是否正确
2. **权限拒绝**: 检查文件读写权限
3. **格式错误**: 确认文件格式符合要求
4. **超时错误**: 增加超时时间配置
5. **内存不足**: 减少并发任务数或增加内存限制
