"""
Warning Code Generation Workflow
警告代码生成工作流
"""
from typing import Optional

from sdw_agent.service import BaseWorkflow
from sdw_agent.service.dev_env_check.models import PackageFileCompareInfo, CompareResult
from sdw_agent.service.dev_env_check.util.jenkins_script_check_util import compare_jenkins_scripts
from sdw_agent.service.dev_env_check.util.manifest_diff_util import compare_manifest_files
from sdw_agent.service.dev_env_check.util.package_files_compare_util import compare_bin_packages_service


class DeVEnvCheckWorkflow(BaseWorkflow):
    """警告代码生成工作流"""

    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)

    def execute(self, config: PackageFileCompareInfo):
        """
        执行警告代码生成工作流

        Args:
            config: 警告代码生成配置

        Returns:
            警告代码生成结果
        """
        try:
            output = CompareResult()
            self.logger.info("开始执行警告代码生成工作流")

            # 判断 pack_path 是否为空
            if config.pre_pack_path and config.after_pack_path:
                output.bin_compare_excel_path = compare_bin_packages_service(config)
                self.logger.info("bin包对比工作流执行完成")
            else:
                self.logger.info("pack_path 为空，跳过 bin包对比工作流")

            # 判断 manifest_path 是否为空
            if config.pre_manifest_path and config.after_manifest_path:
                output.manifest_compare_excel_path = compare_manifest_files(config)
                self.logger.info("manifest对比工作流执行完成")
            else:
                self.logger.info("manifest_path 为空，跳过 manifest对比工作流")

            # 判断 jenkins_script_path 是否为空
            if config.pre_jenkins_script_path and config.after_jenkins_script_path:
                output.jenkins_script_compare_excel_path = compare_jenkins_scripts(config)
                self.logger.info("jenkins脚本对比工作流执行完成")
            else:
                self.logger.info("jenkins_script_path 为空，跳过 jenkins脚本对比工作流")

            output.genetate_status = True
            return output
        except Exception as e:
            self.logger.error(f"警告代码生成工作流执行失败: {str(e)}")
            raise e

if __name__ == '__main__':
    # 创建工作流实例
    workflow = DeVEnvCheckWorkflow()

    # 准备配置数据
    config = PackageFileCompareInfo(
        pre_pack_path=r"/Orca3/263D/release-build/20230214164344-v3.0.1_3/263D-v301_3-20230214.zip",
        after_pack_path=r"/Orca3/263D/release-build/20230506153126-v3.1.8/263D-v318-20230506.zip",
        pre_manifest_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\manifest\release4.2.0.xml",
        after_manifest_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\manifest\release6.1.0.xml",
        pre_jenkins_script_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\jenkins\configOutput2024-01-05_16-22-06.xml",
        after_jenkins_script_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\jenkins\configOutput2024-01-05_16-24-02.xml"
    )

    # 执行工作流
    result = workflow.execute(config)
