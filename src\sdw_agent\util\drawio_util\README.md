# Draw.io 导出到 Excel 工具

## 🎯 功能概述

这个工具可以将 Draw.io 图表**原始图像**插入到 Excel 的**固定单元格**中，保持完整的视觉效果。

## ✨ 特性

- ✅ **保持原始图表** - 完整保留线条、颜色、布局
- ✅ **固定单元格位置** - 精确控制图像在 Excel 中的位置
- ✅ **自定义尺寸** - 可设置图像宽度和高度
- ✅ **支持列字母** - 可使用 A、B、C 等列字母
- ✅ **智能导出方式** - 优先使用本地 draw.io，回退到 Playwright
- ✅ **高质量图像** - 本地软件导出质量更佳，速度更快
- ✅ **全自动化** - 一行代码完成导出

## 📋 安装依赖

### 方式1：使用本地 draw.io (推荐)

下载并安装 [draw.io desktop](https://github.com/jgraph/drawio-desktop/releases)

### 方式2：使用 Playwright (备用)

```bash
# 安装 Playwright
poetry add playwright

# 安装浏览器
poetry run playwright install chromium
```

**注意**: 工具会自动检测本地 draw.io，如果没有安装则自动使用 Playwright。

## 🚀 快速使用

### 基本用法

```python
from sdw_agent.util.drawio_util import drawio_to_excel

# 导出到 B5 单元格
result = drawio_to_excel(
    excel_file="my_diagram.xlsx",
    sheet_name="架构图",
    title="系统架构图",
    image_row=5,        # 第5行
    image_col=2,        # 第2列 (B列)
    image_width=600,    # 宽度600像素
    image_height=400    # 高度400像素
)

if result['success']:
    print(f"✅ 成功！Excel文件: {result['excel_file']}")
    print(f"📍 图像位置: {result['image_position']}")
else:
    print(f"❌ 失败: {result['message']}")
```

### 使用列字母

```python
# 导出到 D3 单元格
result = drawio_to_excel(
    excel_file="diagram_D3.xlsx",
    image_row=3,
    image_col="D",      # 使用列字母
    image_width=800,
    image_height=500
)
```

### 无标题直接插入

```python
# 直接插入到 A1，不添加标题
result = drawio_to_excel(
    excel_file="clean_diagram.xlsx",
    title="",           # 空标题
    image_row=1,
    image_col=1,        # A1 单元格
    image_width=1000,
    image_height=700
)
```

## 📊 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `drawio_file` | str | None | Draw.io文件路径，None时使用模板管理器 |
| `excel_file` | str | "output/architecture_diagram.xlsx" | Excel输出文件路径 |
| `sheet_name` | str | "架构图" | 工作表名称 |
| `title` | str | "系统架构图" | 图表标题，空字符串表示无标题 |
| `image_row` | int | 3 | 图像插入的行号（从1开始） |
| `image_col` | int/str | 1 | 图像插入的列号（数字或字母） |
| `image_width` | int | None | 图像宽度（像素），None表示自动 |
| `image_height` | int | None | 图像高度（像素），None表示自动 |

## 📍 单元格位置说明

### 行号
- 从 1 开始计数
- 例如：`image_row=5` 表示第5行

### 列号
支持两种格式：

1. **数字格式**：`image_col=2` (第2列，即B列)
2. **字母格式**：`image_col="B"` (B列)

### 常用位置对照表

| 位置 | 行号 | 列号(数字) | 列号(字母) |
|------|------|------------|------------|
| A1 | 1 | 1 | "A" |
| B3 | 3 | 2 | "B" |
| C5 | 5 | 3 | "C" |
| D10 | 10 | 4 | "D" |
| F15 | 15 | 6 | "F" |

## 🎯 实际使用场景

### 场景1：报告模板

```python
# 在报告模板的指定位置插入架构图
result = drawio_to_excel(
    excel_file="monthly_report.xlsx",
    sheet_name="系统架构",
    title="",  # 不要标题
    image_row=8,   # 第8行开始
    image_col="C", # C列
    image_width=800,
    image_height=600
)
```

### 场景2：技术文档

```python
# 在技术文档中插入多个图表
positions = [
    {"row": 5, "col": "B", "title": "整体架构"},
    {"row": 30, "col": "B", "title": "详细设计"},
]

for pos in positions:
    result = drawio_to_excel(
        excel_file="tech_doc.xlsx",
        title=pos["title"],
        image_row=pos["row"],
        image_col=pos["col"],
        image_width=700,
        image_height=500
    )
```

## 🔧 返回结果

成功时返回：
```python
{
    "success": True,
    "message": "导出成功 (使用本地 draw.io 桌面软件)",
    "excel_file": "path/to/excel.xlsx",
    "png_file": "path/to/image.png",
    "sheet_name": "工作表名称",
    "image_position": "行5, 列2",
    "export_method": "本地 draw.io 桌面软件"  # 或 "Playwright 在线导出"
}
```

失败时返回：
```python
{
    "success": False,
    "message": "错误信息",
    "png_file": "path/to/image.png"  # PNG文件可能仍然可用
}
```

## 🧪 测试

运行测试脚本：

```bash
# 简单测试
poetry run python src/sdw_agent/util/drawio_util/test_simple.py
```

## 📁 文件结构

```
src/sdw_agent/util/drawio_util/
├── __init__.py                    # 包初始化
├── drawio_to_excel_final.py       # 主要功能模块
├── simple_drawio_to_excel.py      # 简化版本（结构分析）
├── test_simple.py                 # 测试脚本
├── block_diagram.drawio           # 示例文件
└── README.md                      # 本文档
```

## ⚠️ 注意事项

1. **依赖要求**：需要安装 Playwright 和 Chromium
2. **网络连接**：需要访问 app.diagrams.net
3. **文件权限**：确保有 Excel 文件的写入权限
4. **Excel 进程**：如果 Excel 文件被占用，可能导致保存失败

## 🐛 故障排除

### 问题1：Playwright 未安装
```bash
poetry add playwright
poetry run playwright install chromium
```

### 问题2：网络连接问题
- 检查是否能访问 https://app.diagrams.net
- 考虑使用代理或VPN

### 问题3：Excel 文件被占用
- 关闭 Excel 应用程序
- 检查是否有其他程序在使用该文件

### 问题4：图像插入失败
- 检查 PNG 文件是否生成成功
- 可以手动将 PNG 文件插入到 Excel 中

## 📞 支持

如有问题，请检查：
1. 日志输出中的错误信息
2. 生成的 PNG 文件是否正常
3. Excel 文件是否可以正常打开
