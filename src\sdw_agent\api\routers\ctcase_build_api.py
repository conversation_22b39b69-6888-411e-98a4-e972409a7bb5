from typing import Any
from fastapi import APIRouter, HTTPException, status
from loguru import logger
from pydantic import BaseModel

from sdw_agent.service.ctcase_build.workflow_ctcase_build import CtCaseBuildWorkflow
from sdw_agent.service.ctcase_build.models import CtCaseBuildInput, CtCaseBuildResult

# 创建路由器
router = APIRouter(prefix="/api/sdw/ctcase", tags=["CT测试用例构建"])

# API模型定义
class PreEditRequest(BaseModel):
    """编辑前处理请求参数"""
    group_file: str
    kanzi_file: str

class PostEditRequest(BaseModel):
    """编辑后处理请求参数"""
    group_file: str = ""
    kanzi_file: str = ""

class CTCaseResponse(BaseModel):
    """CT测试用例构建响应模型"""
    code: int = 0
    msg: str = ""
    data: str

# 编辑前处理接口
@router.post(
    "/pre_edit",
    summary="执行编辑前处理",
    description="读取Excel数据并生成待编辑的Excel文件",
    response_description="",
    response_model=CTCaseResponse,
)
async def pre_edit(request: PreEditRequest):
    try:
        workflow = CtCaseBuildWorkflow()
        input_data = CtCaseBuildInput(
            group_file=request.group_file,
            kanzi_file=request.kanzi_file,
        )
        result = workflow.execute_pre_edit(input_data)

        logger.info(f"编辑前处理完成: {result.data.get('excel_path')}")
        return CTCaseResponse(
            code=0,
            msg="编辑前处理完成",
            data=result.data.get('excel_path')
        )


    except ValueError as e:
        logger.error(f"参数验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"参数错误: {str(e)}"
        )
    except Exception as e:
        logger.error(f"编辑前处理失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理失败: {str(e)}"
        )

# 编辑后处理接口
@router.post(
    "/post_edit",
    summary="执行编辑后处理",
    description="读取用户编辑后的Excel并生成最终测试用例",
    response_description="",
    response_model=CTCaseResponse,
)
async def post_edit(request: PostEditRequest):
    try:
        workflow = CtCaseBuildWorkflow()
        input_data = CtCaseBuildInput(
            group_file=request.group_file,
            kanzi_file=request.kanzi_file,
        )
        result = workflow.execute_post_edit(input_data)

        logger.info(f"测试用例生成成功: {result.data.get('spec_file_path')}")
        return CTCaseResponse(
            code=0,
            msg="测试用例生成成功",
            data=result.data.get('excel_path')
        )

    except ValueError as e:
        logger.error(f"参数验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"参数错误: {str(e)}"
        )
    except Exception as e:
        logger.error(f"测试用例生成失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理失败: {str(e)}"
        )