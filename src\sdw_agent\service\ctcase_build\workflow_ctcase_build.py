"""
CT测试用例构建工作流

该模块提供从Excel文件构建CT测试用例的功能：
1. 从Excel文件读取测试用例数据
2. 解析并验证测试用例数据
3. 生成CT测试用例
"""
# 修正路径计算 - 需要向上追溯4层才能到达项目根目录
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))  # 使用insert(0)确保优先搜索

import os
import re
import pandas as pd
from itertools import product
from pathlib import Path
from typing import Dict, List, Optional, Any

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow
from sdw_agent.config.env import ENV

from sdw_agent.service.ctcase_build.util.excel_util import (
    process_excel_files, markdown_to_excel, read_edited_excel,
    get_kanziif_value,insert_data_to_template
)
from sdw_agent.service.ctcase_build.models import CtCaseBuildInput, SignalParser
from sdw_agent.service.ctcase_build.util.llm_util import process_combined_data

@register_workflow("ctcase_build")
class CtCaseBuildWorkflow(BaseWorkflow):
    """CT测试用例构建工作流"""
    
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.register_config_model()

    @staticmethod
    def register_config_model():
        """注册配置模型"""
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        config_manager = WorkflowConfigManager(workflow_name="ctcase_build")

    def validate_input(self, operation: str, data: CtCaseBuildInput) -> bool:
        """验证输入参数"""
        try:    
            # 验证Excel文件路径
            if not all([data.group_file, data.kanzi_file]):
                self.logger.error("必须提供完整的Excel文件信息")
                return False
                 
            group_path = Path(data.group_file)
            kanzi_path = Path(data.kanzi_file)
            
            if not group_path.exists():
                self.logger.error(f"グループExcel文件不存在: {data.group_file}")
                return False
            
            if not kanzi_path.exists():
                self.logger.error(f"Kanzi Excel文件不存在: {data.kanzi_file}")
                return False
                 
            return True
        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, operation: str, data: CtCaseBuildInput) -> WorkflowResult:
        """执行工作流，支持预处理(pre_edit)和后处理(post_edit)两种操作"""
        self.logger.info(f"开始执行CT测试用例构建工作流，操作类型: {operation}")
        try:
            if operation == "pre_edit":
                return self.execute_pre_edit(data)
            elif operation == "post_edit":
                return self.execute_post_edit(data)
            else:
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message=f"不支持的操作类型: {operation}",
                    error=f"不支持的操作类型: {operation}"
                )
        except Exception as e:
            self.logger.exception("CT测试用例构建失败")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"CT测试用例构建失败: {str(e)}",
                error=str(e)
            )
        
    def execute_pre_edit(self, input_data: CtCaseBuildInput) -> WorkflowResult:
        """执行编辑前处理流程: 读取Excel数据并生成待编辑Excel文件"""
        if not self.validate_input('pre_edit', input_data):
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message="输入参数验证失败"
            )

        # 1. 读取Excel数据
        combined_data, markdown_table = process_excel_files(
            group_file=input_data.group_file,
            group_sheet="Variable_MID",
            kanzi_file=input_data.kanzi_file,
            kanzi_sheet="Interface"
        )

        # 2. 调用LLM处理数据
        llm_result = process_combined_data(markdown_table, config=self.config['llm_cfg']['ct_build'])

        # 3. 生成Excel供用户编辑
        output_data_path = os.path.normpath(ENV.config.output_data_path)
        if not os.path.exists(output_data_path):
            os.makedirs(output_data_path)
        excel_path = os.path.join(output_data_path, f"llm_result.xlsx")
        markdown_to_excel(llm_result, str(excel_path))
        self.logger.info(f"LLM结果已导出至Excel: {excel_path}")

        return WorkflowResult(
            status=WorkflowStatus.SUCCESS,
            message="编辑前处理完成，请编辑Excel文件后执行post_edit操作",
            data={"excel_path": str(excel_path)}
        )
    
    def execute_post_edit(self, input_data: CtCaseBuildInput) -> WorkflowResult:
        """执行编辑后处理流程: 读取编辑后的Excel并生成最终测试用例
        核心逻辑：数据解析→信号组合生成→矩阵转换→调用Excel工具类导出
        """
        # 1. 读取用户编辑后的Excel
        output_data_path = os.path.normpath(ENV.config.output_data_path)
        os.makedirs(output_data_path, exist_ok=True)
        excel_path = os.path.join(output_data_path, "llm_result.xlsx")

        if not os.path.exists(excel_path):
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"Excel文件不存在: {excel_path}"
            )

        # 2. 数据解析与处理
        edited_df = read_edited_excel(str(excel_path))
        signal_values = self._parse_signal_values(edited_df)  # 提取信号名和可能值
        combination_df = self._generate_signal_combinations(signal_values)  # 生成信号组合
        kanziif_signal_map = self._build_kanziif_mapping(edited_df, combination_df)  # 传入combination_df参数
        matrix_df = self._convert_to_matrix_format(combination_df)  # 转换为矩阵形式

        # 3. 调用Excel工具类处理模板插入
        spec_file_path = os.path.join(output_data_path, "単体テスト仕様書.xlsx")
        try:
            # 调用工具类插入数据到模板
            insert_data_to_template(
                template_path=os.path.join(os.path.dirname(__file__), "【模板】単体テスト仕様書.xlsx"),
                output_path=spec_file_path,
                matrix_df=matrix_df,
                kanziif_signal_map=kanziif_signal_map
            )
            self.logger.info(f"単体テスト仕様書已成功导出至: {spec_file_path}")
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message=f"测试用例生成完成，単体テスト仕様書已导出至: {spec_file_path}",
                data={"excel_path": spec_file_path}
            )
        except Exception as e:
            self.logger.error(f"导出単体テスト仕様書失败: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"生成単体テスト仕様書失败: {str(e)}"
            )

    def _parse_signal_values(self, edited_df):
        """解析表示条件，提取信号名和可能值"""
        signal_values = {}
        for condition in edited_df['表示条件'].unique():
            signal_name, value = SignalParser.parse_condition(condition)
            if signal_name not in signal_values:
                signal_values[signal_name] = []
            parsed_values = SignalParser.generate_values(value)
            signal_values[signal_name].extend(v for v in parsed_values if v not in signal_values[signal_name])
        return signal_values

    def _generate_signal_combinations(self, signal_values):
        """生成所有信号的笛卡尔积组合"""
        signal_names = list(signal_values.keys())
        value_combinations = product(*[signal_values[name] for name in signal_names])
        combination_df = pd.DataFrame(value_combinations, columns=signal_names)
        return combination_df

    def _build_kanziif_mapping(self, edited_df, combination_df):
        """提取KanziIF名称列表并建立与信号名的映射"""
        kanziif_signal_map = {}
        for _, row in edited_df.iterrows():
            signal_name = row['表示条件'].split('=')[0].strip()
            kanziif_name = row['KanziIF名']
            kanziif_signal_map[kanziif_name] = signal_name
            # 添加KanziIF值到组合表
            combination_df[kanziif_name] = combination_df[signal_name].apply(
                lambda value: get_kanziif_value(edited_df, signal_name, value, kanziif_name))
        return kanziif_signal_map

    def _convert_to_matrix_format(self, combination_df):
        """将组合表转换为矩阵形式"""
        combination_df = combination_df.reset_index().rename(columns={'index': 'PCL番号'})
        melted_df = combination_df.melt(id_vars=['PCL番号'], var_name='Parameter', value_name='Value')
        matrix_df = melted_df.pivot(index=['Parameter', 'Value'], columns='PCL番号', values='Value')
        matrix_df = matrix_df.notnull().replace({True: '○', False: ''})
        matrix_df.columns.name = None
        return matrix_df.reset_index()
    

def do_ctcase_build(
    group_file: str, 
    kanzi_file: str, 
    operation: str = "pre_edit"
) -> List[Dict]:
    """执行CT测试用例构建，支持pre_edit和post_edit两种操作模式"""
    workflow = CtCaseBuildWorkflow()
    result = workflow.run(
        operation=operation,
        data=CtCaseBuildInput(
            group_file=group_file,
            kanzi_file=kanzi_file,
        )
    )
    
    if result.status == WorkflowStatus.SUCCESS:
        return result.data.get("excel_path")
    raise Exception(result.message)

if __name__ == "__main__":
    # 示例用法 - 分别指定group和kanzi文件
    group_file = "D:/01.Roc/02.个人作业/单体作成/MET-C_THM-CSTD-A0-01-A-C0.xlsm"
    group_sheet = "Variable_MID"
    kanzi_file = "D:/01.Roc/02.个人作业/单体作成/Meter_Interface_document_DAC_CRAWL.xlsm"
    kanzi_sheet = "Interface"
    
    test_cases = do_ctcase_build(group_file, kanzi_file, operation="pre_edit")

    #test_cases = do_ctcase_build(group_file, kanzi_file, operation="post_edit")
    print(f"构建了 {len(test_cases)} 个测试用例")