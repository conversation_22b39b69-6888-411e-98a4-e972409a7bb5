#!/usr/bin/env python3
"""
最终版本：将 Draw.io 原始图像导出到 Excel
使用 Playwright + win32com，完美解决方案
"""

import os
import urllib.parse
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.service.template_manager import template_manager


def _find_local_drawio_executable() -> Optional[str]:
    """查找本地 draw.io 可执行文件"""
    possible_paths = [
        "drawio",  # 如果在PATH中
        "draw.io",
        r"C:\Program Files\draw.io\draw.io.exe",
        r"C:\Program Files (x86)\draw.io\draw.io.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\draw.io\draw.io.exe".format(os.getenv('USERNAME', '')),
        "/Applications/draw.io.app/Contents/MacOS/draw.io",  # macOS
        "/usr/bin/drawio",  # Linux
        "/usr/local/bin/drawio"
    ]

    for path in possible_paths:
        if _check_executable(path):
            return path
    return None


def _check_executable(path: str) -> bool:
    """检查可执行文件是否存在且可用"""
    try:
        if not os.path.exists(path):
            return False
        import subprocess
        result = subprocess.run([path, "--version"],
                              capture_output=True,
                              text=True,
                              encoding='utf-8',
                              errors='ignore',  # 忽略编码错误
                              timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, OSError, UnicodeDecodeError):
        return False


def _export_with_local_drawio(drawio_file: str, output_png: str, scale: float = 2.0) -> bool:
    """使用本地 draw.io 桌面软件导出图像"""
    drawio_exe = _find_local_drawio_executable()
    if not drawio_exe:
        return False

    try:
        import subprocess

        # 构建命令行参数
        cmd = [
            drawio_exe,
            "--export",
            "--format", "png",
            "--scale", str(scale),
            "--crop",  # 裁剪空白区域
            "--output", output_png,
            drawio_file
        ]

        result = subprocess.run(cmd,
                              capture_output=True,
                              text=True,
                              encoding='utf-8',
                              errors='ignore',  # 忽略编码错误
                              timeout=30)

        if result.returncode == 0 and Path(output_png).exists():
            return True
        else:
            logger.warning(f"本地 draw.io 导出失败: {result.stderr}")
            return False

    except Exception as e:
        logger.warning(f"本地 draw.io 导出异常: {e}")
        return False


def drawio_to_excel(drawio_file: Optional[str] = None,
                   excel_file: str = "output/architecture_diagram.xlsx",
                   sheet_name: str = "架构图",
                   title: str = "系统架构图",
                   image_row: int = 3,
                   image_col: int = 1,
                   image_width: Optional[int] = None,
                   image_height: Optional[int] = None) -> Dict[str, Any]:
    """
    将 Draw.io 文件导出为原始图像并插入 Excel 的指定单元格

    Args:
        drawio_file: Draw.io 文件路径，如果为None则使用模板管理器
        excel_file: Excel 输出文件路径
        sheet_name: 工作表名称
        title: 图表标题
        image_row: 图像插入的行号（从1开始）
        image_col: 图像插入的列号（从1开始，或使用字母如'A','B'等）
        image_width: 图像宽度（像素），None表示自动
        image_height: 图像高度（像素），None表示自动

    Returns:
        操作结果字典
    """
    
    # 1. 检查依赖
    try:
        from playwright.sync_api import sync_playwright
    except ImportError:
        return {
            "success": False,
            "message": "请先安装 Playwright: poetry add playwright && poetry run playwright install chromium"
        }
    
    # 2. 获取 Draw.io 文件
    if drawio_file is None:
        drawio_file = template_manager.get_template_path("block_diagram_file")
        if not drawio_file:
            return {"success": False, "message": "未找到 Draw.io 模板文件"}
    
    if not Path(drawio_file).exists():
        return {"success": False, "message": f"Draw.io 文件不存在: {drawio_file}"}
    
    # 3. 读取文件内容
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
    except Exception as e:
        return {"success": False, "message": f"读取文件失败: {e}"}
    
    # 4. 创建输出文件路径
    output_dir = Path(excel_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    png_file = output_dir / f"drawio_{Path(drawio_file).stem}.png"
    
    # 5. 导出图像 - 优先使用本地 draw.io，回退到 Playwright
    export_success = False
    export_method = ""

    # 方法1：尝试使用本地 draw.io 桌面软件
    try:
        if _export_with_local_drawio(drawio_file, str(png_file)):
            export_success = True
            export_method = "本地 draw.io 桌面软件"
            logger.info("使用本地 draw.io 桌面软件导出成功")
    except Exception as e:
        logger.warning(f"本地 draw.io 导出失败: {e}")

    # 方法2：如果本地方法失败，使用 Playwright
    if not export_success:
        try:
            encoded_content = urllib.parse.quote(xml_content)
            drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1#R{encoded_content}"

            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page()
                page.set_viewport_size({"width": 1920, "height": 1080})
                page.goto(drawio_url, timeout=60000)
                page.wait_for_timeout(8000)  # 等待加载
                page.screenshot(path=str(png_file), full_page=True, type="png")
                browser.close()

            if png_file.exists() and png_file.stat().st_size > 0:
                export_success = True
                export_method = "Playwright 在线导出"
                logger.info("使用 Playwright 在线导出成功")

        except Exception as e:
            logger.error(f"Playwright 导出失败: {e}")

    # 检查导出结果
    if not export_success or not png_file.exists() or png_file.stat().st_size == 0:
        return {"success": False, "message": "所有图像导出方法都失败了"}
    
    # 6. 插入到 Excel
    try:
        with ExcelUtil(excel_file, auto_create=True) as excel:
            # 创建工作表
            if sheet_name not in excel.get_sheet_names():
                excel.create_sheet(sheet_name)
            
            # 添加标题
            excel.write_cell(sheet_name, 1, 1, title)
            excel.set_cell_style(sheet_name, 1, 1, CellStyle(
                font_size=16, font_bold=True, alignment_horizontal="center"
            ))
            
            # 插入图像到指定单元格
            # 处理列号（支持字母和数字）
            col_num = _convert_col_to_number(image_col) if isinstance(image_col, str) else image_col

            success = _insert_image(excel, sheet_name, str(png_file), image_row, col_num, image_width, image_height)
            
            if success:
                excel.save()
                return {
                    "success": True,
                    "message": f"导出成功 (使用{export_method})",
                    "excel_file": excel_file,
                    "png_file": str(png_file),
                    "sheet_name": sheet_name,
                    "image_position": f"行{image_row}, 列{image_col}",
                    "export_method": export_method
                }
            else:
                return {
                    "success": False,
                    "message": "图像插入失败",
                    "png_file": str(png_file)  # PNG 文件仍然可用
                }
                
    except Exception as e:
        return {
            "success": False,
            "message": f"Excel 操作失败: {e}",
            "png_file": str(png_file)  # PNG 文件仍然可用
        }


def _insert_image(excel: ExcelUtil, sheet_name: str, image_path: str, row: int, col: int,
                 width: Optional[int] = None, height: Optional[int] = None) -> bool:
    """插入图像到 Excel 的指定单元格"""
    try:
        # 方法1：使用 win32com 直接插入
        if hasattr(excel, 'engine') and hasattr(excel.engine, 'app') and excel.engine.app:
            try:
                # 获取工作表
                ws = excel.engine.app.Worksheets(sheet_name)

                # 获取目标单元格的位置
                target_cell = ws.Cells(row, col)
                left = target_cell.Left
                top = target_cell.Top

                # 插入图片 - 使用绝对路径
                abs_image_path = str(Path(image_path).resolve())
                shape = ws.Shapes.AddPicture(
                    Filename=abs_image_path,
                    LinkToFile=False,
                    SaveWithDocument=True,
                    Left=left,
                    Top=top,
                    Width=-1,  # 保持原始比例
                    Height=-1
                )

                # 调整图像大小
                if width or height:
                    if width:
                        shape.Width = width
                    if height:
                        shape.Height = height
                else:
                    # 默认最大宽度限制
                    if shape.Width > 800:
                        scale_factor = 800 / shape.Width
                        shape.Width = 800
                        shape.Height = shape.Height * scale_factor

                logger.info(f"图像已插入到单元格 {_get_cell_name(row, col)}")
                logger.info(f"图像尺寸: {shape.Width} x {shape.Height}")
                return True

            except Exception as e:
                logger.error(f"win32com 直接插入失败: {e}")
                logger.error(f"Excel engine 属性: {dir(excel.engine) if hasattr(excel, 'engine') else 'No engine'}")

        # 方法2：尝试通过 ExcelUtil 的内部方法
        if hasattr(excel, 'engine') and hasattr(excel.engine, 'workbook'):
            try:
                # 切换到指定工作表
                ws = excel.engine.workbook.Worksheets(sheet_name)
                ws.Activate()

                # 获取目标单元格的位置
                target_cell = ws.Cells(row, col)
                left = target_cell.Left
                top = target_cell.Top

                # 插入图片 - 使用绝对路径
                abs_image_path = str(Path(image_path).resolve())
                shape = ws.Shapes.AddPicture(
                    Filename=abs_image_path,
                    LinkToFile=False,
                    SaveWithDocument=True,
                    Left=left,
                    Top=top,
                    Width=-1,
                    Height=-1
                )

                # 调整大小
                if width or height:
                    if width:
                        shape.Width = width
                    if height:
                        shape.Height = height
                else:
                    if shape.Width > 800:
                        scale_factor = 800 / shape.Width
                        shape.Width = 800
                        shape.Height = shape.Height * scale_factor

                logger.info(f"通过 workbook 插入图像成功: {_get_cell_name(row, col)}")
                return True

            except Exception as e:
                logger.error(f"通过 workbook 插入失败: {e}")

        logger.error("所有图像插入方法都失败了")
        logger.error(f"Excel 对象属性: {dir(excel)}")
        if hasattr(excel, 'engine'):
            logger.error(f"Engine 属性: {dir(excel.engine)}")

        return False

    except Exception as e:
        logger.error(f"图像插入异常: {e}")
        return False


def _convert_col_to_number(col_letter: str) -> int:
    """将列字母转换为数字 (A=1, B=2, ..., Z=26, AA=27, etc.)"""
    col_letter = col_letter.upper()
    result = 0
    for char in col_letter:
        result = result * 26 + (ord(char) - ord('A') + 1)
    return result


def _get_cell_name(row: int, col: int) -> str:
    """获取单元格名称 (如 A1, B2, etc.)"""
    col_letter = ""
    while col > 0:
        col -= 1
        col_letter = chr(col % 26 + ord('A')) + col_letter
        col //= 26
    return f"{col_letter}{row}"


def quick_export():
    """快速导出当前项目的 Draw.io 图表"""
    print("🚀 快速导出 Draw.io 图表到 Excel")
    print("=" * 40)
    
    result = drawio_to_excel(
        excel_file="output/quick_export.xlsx",
        title="项目架构图"
    )
    
    print(f"结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
    print(f"消息: {result['message']}")
    
    if result['success']:
        print(f"📊 Excel文件: {result['excel_file']}")
        print(f"📸 PNG文件: {result['png_file']}")
        print(f"📋 工作表: {result['sheet_name']}")
        print("\n🎉 完成！请打开 Excel 文件查看原始图表")
    elif 'png_file' in result:
        print(f"💡 PNG 文件已生成: {result['png_file']}")
        print("   可以手动插入到 Excel 中")


def batch_export(drawio_files: list, output_dir: str = "output/batch_export"):
    """批量导出多个 Draw.io 文件"""
    print(f"📦 批量导出 {len(drawio_files)} 个文件")
    print("=" * 40)
    
    results = []
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    for i, drawio_file in enumerate(drawio_files, 1):
        print(f"\n{i}/{len(drawio_files)} 处理: {Path(drawio_file).name}")
        
        excel_file = output_path / f"{Path(drawio_file).stem}.xlsx"
        
        result = drawio_to_excel(
            drawio_file=drawio_file,
            excel_file=str(excel_file),
            title=f"架构图 - {Path(drawio_file).stem}"
        )
        
        results.append({
            "file": drawio_file,
            "result": result
        })
        
        print(f"   {'✅' if result['success'] else '❌'} {result['message']}")
    
    # 统计结果
    success_count = sum(1 for r in results if r['result']['success'])
    print(f"\n📊 批量导出完成:")
    print(f"   成功: {success_count}/{len(drawio_files)}")
    print(f"   输出目录: {output_dir}")
    
    return results


def demo():
    """演示所有功能"""
    print("🎯 Draw.io 到 Excel 导出工具演示")
    print("=" * 50)
    
    # 1. 快速导出
    print("\n1️⃣ 快速导出测试")
    quick_export()
    
    # 2. 自定义导出
    print("\n2️⃣ 自定义导出测试")
    result = drawio_to_excel(
        excel_file="output/custom_export.xlsx",
        sheet_name="自定义架构图",
        title="我的系统架构图"
    )
    print(f"自定义导出: {'✅' if result['success'] else '❌'} {result['message']}")
    
    print(f"\n🎉 演示完成！请查看 output 目录中的文件。")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            quick_export()
        elif sys.argv[1] == "demo":
            demo()
        else:
            print("用法: python drawio_to_excel_final.py [quick|demo]")
    else:
        demo()
