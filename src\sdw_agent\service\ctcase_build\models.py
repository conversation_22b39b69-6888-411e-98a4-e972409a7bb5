from pydantic import BaseModel
from typing import Optional,Dict, List, Tuple
from pydantic import BaseModel, Field
import re




class CtCaseBuildInput(BaseModel):
    """CT测试用例构建输入参数"""
    group_file: str          # グループ数据Excel文件路径
    kanzi_file: str          # Kanzi数据Excel文件路径

class CtCaseBuildResult(BaseModel):
    """CT测试用例构建结果"""
    status: str
    reason: str
    data: Optional[dict] = None


class ConfirmFormatCtCase(BaseModel):
    """LLM确认结果模型"""
    data: str = Field(description="Markdown格式化状态")

class SignalParser:
    """信号解析器，处理表示条件解析和值生成"""
    @staticmethod
    def parse_condition(condition: str) -> Tuple[str, str]:
        """解析表示条件字符串，提取信号名和值"""
        pattern = re.compile(r'^([^=]+?)\s*=\s*(.+)$')
        match = pattern.match(condition.strip())
        if not match:
            raise ValueError(f"无效的表示条件格式: {condition}")
        return match.groups()

    @staticmethod
    def generate_values(value_str: str) -> List[str]:
        """根据值字符串生成可能的取值列表"""
        if '*' in value_str:
            return [str(i) for i in range(4)]
        elif value_str.lower().endswith('b'):
            bit_length = len(value_str[:-1])
            max_value = (1 << bit_length) - 1
            return [str(i) for i in range(max_value + 1)]
        return [value_str]