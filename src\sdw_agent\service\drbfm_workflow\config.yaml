# DRBFM工作流配置文件
# DRBFM (Design Review Based on Failure Mode) 工作流配置

# 基本信息
name: "DRBFM工作流"
description: "基于故障模式的设计评审工作流"
version: "1.0.0"
author: "SDW Agent"

# 工作流常量
workflow_constants:
  # 默认值
  default_timeout: 600  # 默认超时时间（秒）
  default_retry_count: 3  # 默认重试次数
  default_output_format: "excel"  # 默认输出格式
  
  # 文件扩展名
  supported_file_extensions:
    - ".xlsx"
    - ".xls"
    - ".drawio"
    - ".pdf"
  
  # DRBFM步骤名称
  workflow_steps:
    - "要件和SCL分析"
    - "变更概要写入DRBFM"
    - "Block图生成并写入DRBFM"
    - "变更点、变化点比较作成，写入DRBFM"
    - "担心点抽出表作成，写入DRBFM"
    - "FTA作成，写入DRBFM"
    - "DRBFM sheet页作成"

# 模块特定配置
module_specific:
  # 分析配置
  analysis:
    max_requirements_count: 1000  # 最大要件数量
    max_scl_items_count: 500     # 最大SCL项目数量
    analysis_timeout: 300        # 分析超时时间
  
  # Block图配置
  block_diagram:
    max_components_count: 100    # 最大组件数量
    highlight_color: "red"       # 高亮颜色
    diagram_format: "drawio"     # 图表格式
  
  # 变更点分析配置
  change_point:
    max_change_points: 50        # 最大变更点数量
    comparison_depth: 3          # 比较深度
  
  # 担心点配置
  concern:
    max_concern_categories: 20   # 最大担心点分类数量
    severity_levels:             # 严重程度等级
      low: 1
      medium: 2
      high: 3
      critical: 4
  
  # FTA配置
  fta:
    max_failure_modes: 30        # 最大故障模式数量
    probability_threshold: 0.01  # 概率阈值
  
  # Sheet配置
  sheet:
    max_sections: 10             # 最大部分数量
    template_format: "excel"     # 模板格式

# 消息常量
message_constants:
  # 成功消息
  success_analysis: "成功完成要件和SCL分析"
  success_summary: "成功写入变更概要"
  success_block_diagram: "成功生成Block图"
  success_change_point: "成功作成变更点、变化点比较"
  success_concern: "成功作成担心点抽出表"
  success_fta: "成功作成FTA"
  success_sheet: "成功作成DRBFM sheet页"
  
  # 错误消息
  error_file_not_found: "文件未找到"
  error_invalid_format: "文件格式无效"
  error_analysis_failed: "分析失败"
  error_generation_failed: "生成失败"
  error_write_failed: "写入失败"
  
  # 警告消息
  warning_large_file: "文件过大，可能影响性能"
  warning_timeout: "操作超时"

# 输出配置
output:
  # 输出目录
  base_output_dir: "output/drbfm_workflow"
  
  # 子目录
  subdirectories:
    analysis: "analysis"
    diagrams: "diagrams"
    reports: "reports"
    temp: "temp"
  
  # 文件命名模式
  file_naming:
    analysis_result: "analysis_{project_id}_{timestamp}.xlsx"
    block_diagram: "block_diagram_{project_id}_{timestamp}.drawio"
    fta_diagram: "fta_{project_id}_{timestamp}.drawio"
    drbfm_sheet: "drbfm_sheet_{project_id}_{timestamp}.xlsx"

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  
  # 日志文件配置
  file_logging:
    enabled: true
    rotation: "1 day"
    retention: "30 days"
    compression: "gz"

# 性能配置
performance:
  # 并发配置
  max_concurrent_tasks: 5      # 最大并发任务数
  task_timeout: 600           # 任务超时时间
  
  # 内存配置
  max_memory_usage: "2GB"     # 最大内存使用量
  
  # 缓存配置
  cache_enabled: true         # 启用缓存
  cache_size: 100            # 缓存大小
  cache_ttl: 3600            # 缓存生存时间（秒）

# 验证配置
validation:
  # 输入验证
  input_validation:
    enabled: true
    strict_mode: false
  
  # 输出验证
  output_validation:
    enabled: true
    check_file_integrity: true
  
  # 数据验证
  data_validation:
    max_string_length: 1000
    max_list_size: 100
    max_dict_size: 50
