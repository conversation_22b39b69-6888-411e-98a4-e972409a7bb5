"""
DRBFM工作流测试

测试DRBFM工作流的各个步骤是否能正常执行
"""

import pytest
from sdw_agent.service.drbfm_workflow import (
    DrbfmWorkflow,
    DrbfmAnalysisRequest,
    DrbfmSummaryRequest,
    DrbfmBlockDiagramRequest,
    DrbfmChangePointRequest,
    DrbfmConcernRequest,
    DrbfmFtaRequest,
    DrbfmSheetRequest
)
from sdw_agent.service import WorkflowStatus
from sdw_agent.model.request_model import SourceInfo


class TestDrbfmWorkflow:
    """DRBFM工作流测试类"""

    def setup_method(self):
        """测试前准备"""
        self.workflow = DrbfmWorkflow()

    def test_workflow_initialization(self):
        """测试工作流初始化"""
        assert self.workflow is not None
        assert hasattr(self.workflow, 'execute_analysis')
        assert hasattr(self.workflow, 'execute_summary')
        assert hasattr(self.workflow, 'execute_block_diagram')
        assert hasattr(self.workflow, 'execute_change_point')
        assert hasattr(self.workflow, 'execute_concern')
        assert hasattr(self.workflow, 'execute_fta')
        assert hasattr(self.workflow, 'execute_sheet')

    def test_execute_analysis(self):
        """测试要件和SCL分析"""
        request = DrbfmAnalysisRequest(
            RequirementSource=SourceInfo(type="local", uri="/test/requirements.xlsx"),
            SclTemplateSource=SourceInfo(type="local", uri="/test/scl_template.xlsx")
        )

        result = self.workflow.execute_analysis(request)

        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "analysis_result" in result.data
        assert "requirements_summary" in result.data
        assert "scl_summary" in result.data

    def test_execute_summary(self):
        """测试变更概要写入"""
        # 先执行分析步骤以设置前置条件
        analysis_request = DrbfmAnalysisRequest(
            RequirementSource=SourceInfo(type="local", uri="/test/requirements.xlsx"),
            SclTemplateSource=SourceInfo(type="local", uri="/test/scl_template.xlsx")
        )
        self.workflow.execute_analysis(analysis_request)

        # 测试带参数的变更概要写入
        request = DrbfmSummaryRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            change_summary="测试变更概要",
            change_reason="测试变更理由",
            change_scope="测试变更范围"
        )

        result = self.workflow.execute_summary(request)

        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "drbfm_file_path" in result.data
        assert "summary_section" in result.data
        assert "workflow_progress" in result.data

    def test_execute_summary_without_params(self):
        """测试无参数的变更概要写入"""
        # 先执行分析步骤以设置前置条件
        analysis_request = DrbfmAnalysisRequest(
            RequirementSource=SourceInfo(type="local", uri="/test/requirements.xlsx"),
            SclTemplateSource=SourceInfo(type="local", uri="/test/scl_template.xlsx")
        )
        self.workflow.execute_analysis(analysis_request)

        # 测试无参数的变更概要写入
        result = self.workflow.execute_summary()

        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "workflow_progress" in result.data
        assert "next_step" in result.data

    def test_execute_block_diagram(self):
        """测试Block图生成"""
        # 先执行前置步骤
        analysis_request = DrbfmAnalysisRequest(
            RequirementSource=SourceInfo(type="local", uri="/test/requirements.xlsx"),
            SclTemplateSource=SourceInfo(type="local", uri="/test/scl_template.xlsx")
        )
        self.workflow.execute_analysis(analysis_request)
        self.workflow.execute_summary()

        request = DrbfmBlockDiagramRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            system_components=["Component1", "Component2", "Component3"],
            component_relationships={"Component1": ["Component2"]},
            highlight_components=["Component1"]
        )

        result = self.workflow.execute_block_diagram(request)

        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "block_diagram_path" in result.data
        assert "highlighted_components" in result.data
        assert "workflow_progress" in result.data

    def test_execute_change_point(self):
        """测试变更点、变化点比较"""
        request = DrbfmChangePointRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            before_state={"state": "before"},
            after_state={"state": "after"},
            change_points=["变更点1", "变更点2"],
            impact_analysis="测试影响分析"
        )
        
        result = self.workflow.execute_change_point(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "comparison_table" in result.data
        assert "change_impact" in result.data

    def test_execute_concern(self):
        """测试担心点抽出表作成"""
        request = DrbfmConcernRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            concern_categories=["安全性", "可靠性"],
            risk_factors=[{"factor": "风险因子1"}],
            severity_levels={"安全性": 3, "可靠性": 2}
        )
        
        result = self.workflow.execute_concern(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "concern_table" in result.data
        assert "risk_assessment" in result.data

    def test_execute_fta(self):
        """测试FTA作成"""
        request = DrbfmFtaRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            failure_modes=["故障模式1", "故障模式2"],
            root_causes=[{"cause": "根本原因1"}],
            probability_data={"故障模式1": 0.01}
        )
        
        result = self.workflow.execute_fta(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "fta_diagram_path" in result.data
        assert "failure_analysis" in result.data

    def test_execute_sheet(self):
        """测试DRBFM sheet页作成"""
        request = DrbfmSheetRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            project_info={"project_name": "测试项目"},
            review_members=["成员1", "成员2"],
            review_date="2024-01-01"
        )
        
        result = self.workflow.execute_sheet(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "sheet_sections" in result.data
        assert "completion_status" in result.data

    def test_execute_with_invalid_operation(self):
        """测试无效操作类型"""
        result = self.workflow.execute("invalid_operation", {})
        
        assert result.status == WorkflowStatus.FAILED
        assert "不支持的操作类型" in result.message

    def test_request_creation(self):
        """测试请求模型创建"""
        # 测试要件和SCL分析请求创建
        analysis_request = DrbfmAnalysisRequest(
            RequirementSource=SourceInfo(type="local", uri="/test/requirements.xlsx"),
            SclTemplateSource=SourceInfo(type="local", uri="/test/scl_template.xlsx")
        )
        assert analysis_request.RequirementSource.uri == "/test/requirements.xlsx"
        assert analysis_request.SclTemplateSource.uri == "/test/scl_template.xlsx"

        # 测试变更概要请求创建
        summary_request = DrbfmSummaryRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            change_summary="测试变更概要",
            change_reason="测试变更理由",
            change_scope="测试变更范围"
        )
        assert summary_request.drbfm_file_path == "/test/drbfm.xlsx"
        assert summary_request.change_summary == "测试变更概要"

        # 测试Block图请求创建
        block_request = DrbfmBlockDiagramRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            system_components=["Component1", "Component2"],
            component_relationships={},
            highlight_components=[]
        )
        assert len(block_request.system_components) == 2

    def test_workflow_state_management(self):
        """测试工作流状态管理"""
        # 测试初始状态
        assert self.workflow.get_current_step() == "analysis"
        assert self.workflow.workflow_state["current_step"] == 0

        # 执行第一步
        analysis_request = DrbfmAnalysisRequest(
            RequirementSource=SourceInfo(type="local", uri="/test/requirements.xlsx"),
            SclTemplateSource=SourceInfo(type="local", uri="/test/scl_template.xlsx")
        )
        result = self.workflow.execute_analysis(analysis_request)

        # 检查状态更新
        assert self.workflow.is_step_completed("analysis")
        assert self.workflow.get_current_step() == "summary"
        assert self.workflow.workflow_state["requirement_source"] is not None
        assert self.workflow.workflow_state["drbfm_file_path"] is not None

        # 执行第二步
        result = self.workflow.execute_summary()
        assert self.workflow.is_step_completed("summary")
        assert self.workflow.get_current_step() == "block_diagram"

        # 测试进度信息
        progress = self.workflow.get_workflow_progress()
        assert progress["current_step"] == "block_diagram"
        assert len(progress["completed_steps"]) == 2
        assert progress["progress"] == "2/7"

        # 测试重置
        self.workflow.reset_workflow()
        assert self.workflow.get_current_step() == "analysis"
        assert len(self.workflow.workflow_state["completed_steps"]) == 0

    def test_workflow_step_dependencies(self):
        """测试工作流步骤依赖"""
        # 尝试在没有完成前置步骤的情况下执行后续步骤
        result = self.workflow.execute_summary()
        assert result.status == WorkflowStatus.FAILED
        assert "前置步骤未完成" in result.error


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
