import openpyxl
import re
import os
import pandas as pd

from typing import Dict,Any
from pathlib import Path
from loguru import logger
from openpyxl.utils.dataframe import dataframe_to_rows

from openpyxl import load_workbook
import os

def insert_data_to_template(template_path, output_path, matrix_df, kanziif_signal_map):
    """将矩阵数据插入到Excel模板的指定位置
    Args:
        template_path: 模板文件路径
        output_path: 输出文件路径
        matrix_df: 要插入的矩阵数据
        kanziif_signal_map: KanziIF与信号的映射关系
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 加载模板并解除合并单元格
    wb = load_workbook(template_path)
    ws = wb.active
    
    # 查找标记行
    input_row = _find_marker_row(ws, "入力条件")
    output_row = _find_marker_row(ws, "出　力　結　果(期待値)")
    if not input_row or not output_row:
        raise ValueError("模板中未找到指定的标记行")
    
    # 分割数据并插入
    signals = list(kanziif_signal_map.values())
    signal_df = matrix_df[matrix_df['Parameter'].isin(signals)]
    kanzi_df = matrix_df[matrix_df['Parameter'].isin(kanziif_signal_map.keys())]
    
    _insert_data(ws, signal_df, input_row)
    _insert_data(ws, kanzi_df, output_row)
    
    # 保存文件
    wb.save(output_path)

def _find_marker_row(worksheet, marker_text):
    """在A列查找包含标记文本的行号"""
    for row in range(1, worksheet.max_row + 1):
        if worksheet.cell(row=row, column=1).value == marker_text:
            return row
    return None

def _insert_data(worksheet, df, start_row):
    """将DataFrame数据插入到指定起始行"""
    for r_idx, (_, row) in enumerate(df.iterrows(), start=start_row):
        for c_idx, value in enumerate(row):
            target_col = _get_target_col(c_idx)
            worksheet.cell(row=r_idx, column=target_col, value=value)

def _get_target_col(c_idx):
    """列索引映射：原A列→B列，原B列→I列，原C列及之后→P列之后"""
    if c_idx == 0:  # 原A列(Parameter)
        return 2   # B列
    elif c_idx == 1:  # 原B列(Value)
        return 9   # I列
    else:  # 原C列及之后(PCL编号)
        return 16 + (c_idx - 2)  # P列之后
    
def markdown_to_excel(markdown_table, output_path):
    lines = markdown_table.strip().split('\n')
    if not lines:
        raise ValueError("Empty markdown table")
    
    # 解析表头
    headers = [h.strip() for h in lines[0].split('|')[1:-1]]
    
    # 跳过分隔线（第二行）并解析数据行
    data = []
    for line in lines[2:]:
        line = line.strip()
        if not line or line.startswith('|--'):
            continue
        row = [cell.strip() for cell in line.split('|')[1:-1]]
        data.append(row)
    
    # 创建 DataFrame
    df = pd.DataFrame(data, columns=headers)
    df.to_excel(output_path, index=False, engine='openpyxl')
    logger.info(f"Excel文件已生成: {output_path}")
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, engine='openpyxl', sheet_name='Sheet1')
        worksheet = writer.sheets['Sheet1']
        
        # 自动调整列宽
        for column_cells in worksheet.columns:
            max_length = max(len(str(cell.value)) for cell in column_cells)
            # 设置列宽，加2是为了留有余地，1.2是经验系数
            adjusted_width = (max_length + 2) * 1.2
            worksheet.column_dimensions[column_cells[0].column_letter].width = adjusted_width

def get_kanziif_value(df: pd.DataFrame, signal_name: str, value: str, kanziif_name: str) -> Any:
    """根据单个信号条件查找对应的KanziIF值"""
    binary_value = f"{bin(int(value))[2:].zfill(2)}b"
    mask = df['表示条件'].str.contains(f'^{signal_name}\s*=\s*{re.escape(binary_value)}$') & (df['KanziIF名'] == kanziif_name)
    
    matched_rows = df[mask]
    if not matched_rows.empty:
        return matched_rows.iloc[0]['KanziIF定义'].split(':')[0].strip()
    return None

def generate_matrix_excel(combination_df: pd.DataFrame, output_path: str) -> str:
    """生成并导出KanziIF组合矩阵Excel文件"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            combination_df.to_excel(writer, index=False, sheet_name='KanziIF_Matrix')
        logger.info(f"KanziIF全组合表已成功导出至: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"导出KanziIF组合表失败: {str(e)}")
        raise

def read_edited_excel(excel_path: str) -> pd.DataFrame:
    """读取用户编辑后的Excel文件"""
    if not Path(excel_path).exists():
        raise FileNotFoundError(f"Excel文件不存在: {excel_path}")
    return pd.read_excel(excel_path, engine='openpyxl')


def generate_matrix_table(df: pd.DataFrame) -> str:
    """生成表示条件与KanziIF的全矩阵组合表"""
    # 获取去重后的表示条件和KanziIF
    conditions = df['表示条件'].drop_duplicates().tolist()
    kanzi_interfaces = df['KanziIF名'].drop_duplicates().tolist()

    # 创建空矩阵
    matrix_data = {}
    for cond in conditions:
        matrix_data[cond] = {if_name: '×' for if_name in kanzi_interfaces}

    # 填充矩阵中存在的组合
    for _, row in df.iterrows():
        cond = row['表示条件']
        if_name = row['KanziIF名']
        if cond in matrix_data and if_name in matrix_data[cond]:
            matrix_data[cond][if_name] = '○'

    # 转换为DataFrame并生成Markdown
    matrix_df = pd.DataFrame(matrix_data).T
    matrix_df.index.name = '表示条件'    
    return matrix_df.to_markdown()

def convert_to_markdown_table(merged_data) -> str:
    """将包含Descriptions的项转换为Markdown表格"""
    # 筛选有Descriptions的项
    table_data = []
    for group_name, group_items in merged_data.items():
        for item_id, item_info in group_items.items():
            if 'Descriptions' in item_info and item_info['Descriptions']:
                # 遍历每个KanziIF接口和描述，每个生成一行
                for if_name, desc in zip(item_info['DataInterfaces'], item_info['Descriptions']):
                    table_data.append({
                        'Group': group_name,
                        'ID': item_id,
                        '表示状态': item_info['表示状态'],
                        '表示条件': item_info['表示条件'],
                        'KanziIF名': if_name,
                        'KanziIF定义': desc
                    })
    
    if not table_data:
        logger.warning("未找到包含Descriptions的项")
        return ""
    
    # 手动构建Markdown表格，避免单元格合并
    if not table_data:
        return ""
    
    # 获取表头
    headers = table_data[0].keys()
    # 创建表头行
    markdown = '| ' + ' | '.join(headers) + ' |\n'
    # 创建分隔线行
    markdown += '| ' + ' | '.join(['---'] * len(headers)) + ' |\n'
    # 创建数据行
    for item in table_data:
        row = '| ' + ' | '.join([str(item[header]).replace('\n', ';') for header in headers]) + ' |\n'
        markdown += row
    
    return markdown

def process_excel_files(group_file: str, group_sheet: str, kanzi_file: str, kanzi_sheet: str) -> Dict[str, Dict[str, Dict]]:
    """
    整合处理两个Excel文件的数据
    
    :param group_file: グループ数据Excel文件路径
    :param group_sheet: グループ数据sheet名称
    :param kanzi_file: Kanzi数据Excel文件路径
    :param kanzi_sheet: Kanzi数据sheet名称
    :return: 整合后的数据字典
    """
    # 提取グループ数据
    wb = openpyxl.load_workbook(group_file, data_only=True)
    sheet = wb[group_sheet]
    group_result = {}
    
    for row in sheet.iter_rows():
        if row[3].value and "各表示エリアの表示" in str(row[3].value):
            group1_name = str(row[3].value).split("各表示エリアの表示")[0]
        if row[3].value and "グループ" in str(row[3].value) and "表示要求" in str(row[3].value):
            try:
                group2_name = str(row[3].value).split("グループ")[1].split(" ")[0]
                lines_to_extract = int(row[11].value)
                group_data = {}
                start_row = row[0].row + 1
                
                for i in range(lines_to_extract):
                    b_value = sheet.cell(row=start_row+i+2, column=2).value
                    o_value = sheet.cell(row=start_row+i+2, column=15).value
                    t_value = sheet.cell(row=start_row+i+2, column=20).value
                    
                    if b_value:
                        group_data[b_value] = {
                            "表示状态": o_value if o_value else "",
                            "表示条件": t_value if t_value else ""
                        }
                
                group_result[f"{group1_name}_グループ{group2_name}"] = group_data
            except (ValueError, IndexError, AttributeError):
                continue
    wb.close()

    # 提取Kanzi数据
    wb = openpyxl.load_workbook(kanzi_file, data_only=True)
    sheet = wb[kanzi_sheet]
    kanzi_result = {}
    
    for row in sheet.iter_rows():
        if row[0].row > 2:
            try:
                a_value = row[0].value
                d_value = row[3].value
                i_value = row[8].value
                
                if a_value or d_value or i_value:
                    i_values = str(i_value).split('\n') if i_value else ['']
                    for i_val in i_values:
                        if i_val not in kanzi_result:
                            kanzi_result[i_val] = []
                        kanzi_result[i_val].append({
                            "Description": d_value if d_value else "",
                            "DataInterface": a_value if a_value else ""
                        })
            except (ValueError, IndexError, AttributeError):
                continue
    wb.close()

    # 合并数据
    for group_key, group_data in group_result.items():
        for item_id, item_data in group_data.items():
            if item_id in kanzi_result:
                descriptions = [d["Description"] for d in kanzi_result[item_id] if d["Description"]]
                data_interfaces = [d["DataInterface"] for d in kanzi_result[item_id] if d["DataInterface"]]
                item_data.update({
                    "Descriptions": descriptions,
                    "DataInterfaces": data_interfaces
                })
    markdown_table = convert_to_markdown_table(group_result)
    
    combined_data = {
        "groups": group_result,
        "kanzis": kanzi_result
    }
    
    return combined_data, markdown_table

# 使用示例
if __name__ == "__main__":
    # 文件路径配置
    group_file = "D:/01.Roc/02.个人作业/单体作成/MET-C_THM-CSTD-A0-01-A-C0.xlsm"
    group_sheet = "Variable_MID"
    kanzi_file = "D:/01.Roc/02.个人作业/单体作成/Meter_Interface_document_DAC_CRAWL.xlsm"
    kanzi_sheet = "Interface"
    
    # 处理数据
    combined_data = process_excel_files(group_file, group_sheet, kanzi_file, kanzi_sheet)
    
    # 遍历输出所有数据
    for group_name, group_data in combined_data.items():
        print(f"\n=== グループ: {group_name} ===")
        for item_id, item_info in group_data.items():
            print(f"\nID: {item_id}")
            print(f"表示状态: {item_info['表示状态']}")
            print(f"表示条件: {item_info['表示条件']}")
            if 'Descriptions' in item_info:
                print("Descriptions:")
                for desc in item_info['Descriptions']:
                    print(f"  - {desc}")
            if 'DataInterfaces' in item_info:
                print("DataInterfaces:")
                for di in item_info['DataInterfaces']:
                    print(f"  - {di}")