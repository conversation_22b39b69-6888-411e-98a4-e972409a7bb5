# IF整合性确认工作流配置

# 基本配置
name: "新变表更新"
description: "提供新变表更新功能，检查代码中新变表的更新内容"
version: "1.0.0"
author: "SDW-Team"

#用户配置
user_config:
  ncl_gerrit_url: "http://************:8080"
  ncl_gerrit_username: "cr_robot"
  ncl_gerrit_password: "ocsa@2024!"

# LLM配置
llm_cfg:
  ct_build: >
    ## 任务目标
    输入数据中每一行的KanziIF名对应了多个KanziIF定义，请根据表示状态和表示条件的语义，选择最匹配的唯一的KanziIF定义。

    ## 处理规则
    1. 逐行确认
    2. 对每个KanziIF名，结合表示状态和表示条件的语义进行综合判断
    3. 确保每个KanziIF名只对应一个最匹配的KanziIF定义中的值
    4. 如遇到冲突定义，优先选择与表示状态直接相关的那个

    ## 输出要求
    1.严格按照给定的Markdown表格格式输出，除KanziIF定义外其他列内容均与输入数据保持一致。
    2.除了Markdown表格外任何其他输出都不要有。

    ## 输入数据
    {{input_data}}

    ##输出示例
    | Group | ID | 表示状态 | 表示条件 | KanziIF名 | KanziIF定义 |
    |-------|----|----------|----------|------------|-------------|
    | Group1 | ID1 | 状态1 | 条件1 | KanziIF名1 | 最匹配的值1 |
    | Group2 | ID2 | 状态2 | 条件2 | KanziIF名2 | 最匹配的值2 |

