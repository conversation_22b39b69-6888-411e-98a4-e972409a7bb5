"""
分类规则管理服务

提供分类规则的业务逻辑实现，包括规则的创建、查询、更新和删除功能。
管理规则文件的存储和元数据信息。
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

from loguru import logger
from pydantic import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate

from sdw_agent.config.env import ENV
from sdw_agent.util.excel.core import ExcelUtil
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output


class RuleMetadata(BaseModel):
    """规则元数据模型"""
    rule_key: str = Field(..., description="规则唯一标识")
    rule_name: str = Field(..., description="规则名称")
    rule_description: str = Field(..., description="规则描述")
    role_name: str = Field(..., description="角色名称")
    excel_file_path: str = Field(..., description="Excel文件相对路径")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class CategoryDescription(BaseModel):
    """分类描述模型"""
    category_name: str = Field(..., description="分类名称")
    description: str = Field(..., description="生成的描述")
    reasoning: str = Field(..., description="生成描述的理由")


class CategoryDescriptionList(BaseModel):
    """分类描述列表模型"""
    descriptions: List[CategoryDescription] = Field(..., description="分类描述列表")


class CategoryContent(BaseModel):
    """分类内容模型"""
    category_name: str = Field(..., description="分类名称")
    description: str = Field(..., description="分类描述")
    additional_fields: Dict[str, Any] = Field(default_factory=dict, description="其他字段内容")


class RuleMatchResult(BaseModel):
    """规则匹配结果模型"""
    rule_key: str = Field(..., description="匹配的规则key")
    rule_name: str = Field(..., description="规则名称")
    confidence: float = Field(..., description="匹配置信度")
    reasoning: str = Field(..., description="匹配理由")


class CategoryMatchResult(BaseModel):
    """分类分析结果模型"""
    category_name: str = Field(..., description="分类名称")
    confidence: float = Field(..., description="置信度")
    reasoning: str = Field(..., description="理由")


class SingleCategoryMatchResponse(BaseModel):
    """单个分类匹配响应模型"""
    category_name: str = Field(..., description="分类名称")
    confidence: float = Field(..., description="置信度")
    reasoning: str = Field(..., description="理由")


class SingleRuleMatchResponse(BaseModel):
    """单个规则匹配响应模型"""
    rule_key: str = Field(..., description="匹配的规则key")
    rule_name: str = Field(..., description="规则名称")
    confidence: float = Field(..., description="匹配置信度")
    reasoning: str = Field(..., description="匹配理由")


class ContentPolishResult(BaseModel):
    """内容润色结果模型"""
    field_name: str = Field(..., description="字段名称")
    original_content: str = Field(..., description="原始内容")
    polished_content: str = Field(..., description="润色后的内容")
    reasoning: str = Field(..., description="润色理由")


class GuidelineManager:
    """分类规则管理器"""

    def __init__(self):
        """初始化规则管理器"""
        self.rule_dir = Path(ENV.rule_dir)
        self.metadata_file = self.rule_dir / "rules_metadata.json"
        self._ensure_directories()

    def _ensure_directories(self):
        """确保必要的目录存在"""
        try:
            # 创建规则目录
            self.rule_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"规则目录已确保存在: {self.rule_dir}")

            # 如果元数据文件不存在，创建空的元数据文件
            if not self.metadata_file.exists():
                self._save_metadata({})
                logger.info(f"创建空的元数据文件: {self.metadata_file}")

        except Exception as e:
            logger.error(f"创建目录失败: {str(e)}")
            raise

    def _load_metadata(self) -> Dict[str, RuleMetadata]:
        """加载规则元数据"""
        try:
            if not self.metadata_file.exists():
                return {}

            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return {key: RuleMetadata(**value) for key, value in data.items()}
        except Exception as e:
            logger.error(f"加载元数据失败: {str(e)}")
            return {}

    def _save_metadata(self, metadata: Dict[str, RuleMetadata]):
        """保存规则元数据"""
        try:
            # 转换为可序列化的字典
            data = {}
            for key, rule_meta in metadata.items():
                if isinstance(rule_meta, RuleMetadata):
                    data[key] = rule_meta.model_dump()
                else:
                    data[key] = rule_meta

            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"元数据已保存: {self.metadata_file}")
        except Exception as e:
            logger.error(f"保存元数据失败: {str(e)}")
            raise

    def _get_rule_directory(self, rule_key: str) -> Path:
        """获取规则目录路径"""
        return self.rule_dir / rule_key

    def _copy_excel_file(self, source_path: str, rule_key: str) -> str:
        """复制Excel文件到规则目录"""
        try:
            source_file = Path(source_path)
            if not source_file.exists():
                raise FileNotFoundError(f"源Excel文件不存在: {source_path}")

            # 创建规则目录
            rule_dir = self._get_rule_directory(rule_key)
            rule_dir.mkdir(parents=True, exist_ok=True)

            # 复制文件，保持原文件名
            dest_file = rule_dir / source_file.name
            shutil.copy2(source_file, dest_file)

            # 返回相对路径
            relative_path = f"{rule_key}/{source_file.name}"
            logger.info(f"Excel文件已复制: {source_path} -> {dest_file}")
            return relative_path

        except Exception as e:
            logger.error(f"复制Excel文件失败: {str(e)}")
            raise

    def create_rule(self, rule_key: str, rule_name: str, rule_description: str,
                    role_name: str, excel_file_path: str, auto_enhance: bool = True) -> Dict[str, Any]:
        """
        创建新的分类规则

        Args:
            rule_key: 规则唯一标识
            rule_name: 规则名称
            rule_description: 规则描述
            role_name: 角色名称
            excel_file_path: Excel文件路径
            auto_enhance: 是否自动使用AI增强描述（默认True）

        Returns:
            创建结果

        Raises:
            ValueError: 当规则已存在或参数无效时
            FileNotFoundError: 当Excel文件不存在时
        """
        logger.info(f"创建规则: {rule_key}")

        try:
            # 加载现有元数据
            metadata = self._load_metadata()

            # 检查规则是否已存在
            if rule_key in metadata:
                raise ValueError(f"规则已存在: {rule_key}")

            # 复制Excel文件
            relative_excel_path = self._copy_excel_file(excel_file_path, rule_key)

            # 创建规则元数据
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            rule_metadata = RuleMetadata(
                rule_key=rule_key,
                rule_name=rule_name,
                rule_description=rule_description,
                role_name=role_name,
                excel_file_path=relative_excel_path,
                created_at=current_time,
                updated_at=current_time
            )

            # 保存元数据
            metadata[rule_key] = rule_metadata
            self._save_metadata(metadata)

            logger.info(f"规则创建成功: {rule_key}")

            # 创建基本结果
            result = {
                "rule_key": rule_key,
                "rule_name": rule_name,
                "excel_file_path": relative_excel_path,
                "created_at": current_time
            }

            # 如果启用自动增强，尝试AI增强描述
            if auto_enhance:
                try:
                    logger.info(f"开始自动AI增强规则描述: {rule_key}")
                    enhance_result = self.enhance_rule_descriptions(rule_key, auto_enhance=True)
                    result["ai_enhancement"] = {
                        "enabled": True,
                        "enhanced_count": enhance_result.get("enhanced_count", 0),
                        "total_count": enhance_result.get("total_count", 0),
                        "message": enhance_result.get("message", "")
                    }
                    logger.info(f"AI增强完成: {enhance_result.get('message', '')}")
                except Exception as e:
                    logger.warning(f"AI增强失败，但规则创建成功: {str(e)}")
                    result["ai_enhancement"] = {
                        "enabled": True,
                        "error": str(e),
                        "message": "AI增强失败，但规则创建成功"
                    }
            else:
                result["ai_enhancement"] = {
                    "enabled": False,
                    "message": "未启用AI增强"
                }

            return result

        except Exception as e:
            logger.error(f"创建规则失败: {str(e)}")
            raise

    def get_rule(self, rule_key: str) -> Optional[RuleMetadata]:
        """
        获取指定的规则信息

        Args:
            rule_key: 规则唯一标识

        Returns:
            规则元数据，如果不存在返回None
        """
        logger.info(f"获取规则: {rule_key}")

        try:
            metadata = self._load_metadata()
            rule_metadata = metadata.get(rule_key)

            # 如果规则存在，将相对路径转换为绝对路径
            if rule_metadata:
                # 创建副本避免修改原始数据
                rule_copy = rule_metadata.model_copy()
                rule_copy.excel_file_path = str(self.rule_dir / rule_metadata.excel_file_path)
                return rule_copy

            return None
        except Exception as e:
            logger.error(f"获取规则失败: {str(e)}")
            raise

    def list_rules(self, rule_key: Optional[str] = None, rule_name: Optional[str] = None,
                   page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        获取规则列表
        
        Args:
            rule_key: 规则key过滤条件（可选）
            rule_name: 规则名称过滤条件，支持模糊查询（可选）
            page: 页码
            page_size: 每页大小
            
        Returns:
            规则列表和分页信息
        """
        logger.info(f"获取规则列表: page={page}, page_size={page_size}")

        try:
            metadata = self._load_metadata()
            rules = list(metadata.values())

            # 应用过滤条件
            if rule_key:
                rules = [rule for rule in rules if rule.rule_key == rule_key]

            if rule_name:
                rules = [rule for rule in rules if rule_name.lower() in rule.rule_name.lower()]

            # 计算分页
            total = len(rules)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_rules = rules[start_idx:end_idx]

            # 转换为字典格式，并将相对路径转换为绝对路径
            rules_data = []
            for rule in page_rules:
                rule_dict = rule.model_dump()
                # 将相对路径转换为绝对路径
                rule_dict['excel_file_path'] = str(self.rule_dir / rule.excel_file_path)
                rules_data.append(rule_dict)

            return {
                "rules": rules_data,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }

        except Exception as e:
            logger.error(f"获取规则列表失败: {str(e)}")
            raise

    def update_rule(self, rule_key: str, rule_name: Optional[str] = None,
                    rule_description: Optional[str] = None, role_name: Optional[str] = None,
                    excel_file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        更新规则信息

        Args:
            rule_key: 规则唯一标识
            rule_name: 新的规则名称（可选）
            rule_description: 新的规则描述（可选）
            role_name: 新的角色名称（可选）
            excel_file_path: 新的Excel文件路径（可选）

        Returns:
            更新结果

        Raises:
            ValueError: 当规则不存在时
        """
        logger.info(f"更新规则: {rule_key}")

        try:
            metadata = self._load_metadata()

            if rule_key not in metadata:
                raise ValueError(f"规则不存在: {rule_key}")

            rule_metadata = metadata[rule_key]

            # 更新字段
            if rule_name is not None:
                rule_metadata.rule_name = rule_name

            if rule_description is not None:
                rule_metadata.rule_description = rule_description

            if role_name is not None:
                rule_metadata.role_name = role_name

            if excel_file_path is not None:
                # 获取当前存储的Excel文件的绝对路径
                current_excel_absolute_path = str(self.rule_dir / rule_metadata.excel_file_path)

                # 检查传入的路径是否与当前存储的文件路径相同
                if excel_file_path == current_excel_absolute_path:
                    # 路径相同，表示前端传回的是现有文件的绝对路径，无需更换文件
                    logger.info(f"Excel文件未变更，保持现有文件: {rule_metadata.excel_file_path}")
                elif excel_file_path == rule_metadata.excel_file_path:
                    # 传入的是相对路径且与存储的相对路径相同，无需更换文件
                    logger.info(f"Excel文件未变更，保持现有文件: {rule_metadata.excel_file_path}")
                else:
                    # 路径不同，说明要更换文件
                    if os.path.isabs(excel_file_path):
                        # 绝对路径：检查是否是规则目录外的新文件
                        if not excel_file_path.startswith(str(self.rule_dir)):
                            # 是规则目录外的新文件，需要复制
                            old_excel_path = self.rule_dir / rule_metadata.excel_file_path
                            if old_excel_path.exists():
                                old_excel_path.unlink()

                            # 复制新的Excel文件
                            rule_metadata.excel_file_path = self._copy_excel_file(excel_file_path, rule_key)
                        else:
                            # 是规则目录内的文件，转换为相对路径
                            relative_path = os.path.relpath(excel_file_path, self.rule_dir)
                            if relative_path != rule_metadata.excel_file_path:
                                # 删除旧文件
                                old_excel_path = self.rule_dir / rule_metadata.excel_file_path
                                if old_excel_path.exists():
                                    old_excel_path.unlink()
                                # 更新为新的相对路径
                                rule_metadata.excel_file_path = relative_path
                                logger.info(f"更新Excel文件路径: {relative_path}")
                    else:
                        # 相对路径：检查新的相对路径文件是否存在
                        new_excel_path = self.rule_dir / excel_file_path
                        if new_excel_path.exists():
                            # 删除旧文件
                            old_excel_path = self.rule_dir / rule_metadata.excel_file_path
                            if old_excel_path.exists():
                                old_excel_path.unlink()
                            # 更新为新的相对路径
                            rule_metadata.excel_file_path = excel_file_path
                            logger.info(f"更新Excel文件路径: {excel_file_path}")
                        else:
                            # 文件不存在
                            raise FileNotFoundError(f"Excel文件不存在: {excel_file_path}")

            # 更新时间
            rule_metadata.updated_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 保存元数据
            metadata[rule_key] = rule_metadata
            self._save_metadata(metadata)

            logger.info(f"规则更新成功: {rule_key}")
            return {
                "rule_key": rule_key,
                "updated_at": rule_metadata.updated_at
            }

        except Exception as e:
            logger.error(f"更新规则失败: {str(e)}")
            raise

    def delete_rule(self, rule_key: str) -> Dict[str, Any]:
        """
        删除规则
        
        Args:
            rule_key: 规则唯一标识
            
        Returns:
            删除结果
            
        Raises:
            ValueError: 当规则不存在时
        """
        logger.info(f"删除规则: {rule_key}")

        try:
            metadata = self._load_metadata()

            if rule_key not in metadata:
                raise ValueError(f"规则不存在: {rule_key}")

            # 删除规则目录及其所有文件
            rule_dir = self._get_rule_directory(rule_key)
            if rule_dir.exists():
                shutil.rmtree(rule_dir)
                logger.info(f"删除规则目录: {rule_dir}")

            # 从元数据中删除
            del metadata[rule_key]
            self._save_metadata(metadata)

            logger.info(f"规则删除成功: {rule_key}")
            return {
                "rule_key": rule_key,
                "deleted_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logger.error(f"删除规则失败: {str(e)}")
            raise

    def get_rule_excel_path(self, rule_key: str) -> Optional[str]:
        """
        获取规则的Excel文件绝对路径

        Args:
            rule_key: 规则唯一标识

        Returns:
            Excel文件的绝对路径，如果规则不存在返回None
        """
        rule_metadata = self.get_rule(rule_key)
        if rule_metadata:
            return str(self.rule_dir / rule_metadata.excel_file_path)
        return None

    def _read_excel_categories(self, excel_path: str) -> List[Dict[str, Any]]:
        """
        读取Excel文件中的分类信息

        Args:
            excel_path: Excel文件路径

        Returns:
            分类信息列表，每个元素包含行号、分类名称和描述
        """
        try:
            with ExcelUtil(excel_path, engine="openpyxl") as excel:
                # 获取第一个工作表
                sheet_names = excel.get_sheet_names()
                if not sheet_names:
                    raise ValueError("Excel文件中没有工作表")

                sheet_name = sheet_names[0]
                logger.info(f"读取工作表: {sheet_name}")

                # 读取表头（第1行）
                headers = []
                col = 1
                while True:
                    header = excel.read_cell(sheet_name, 1, col)
                    if not header or str(header).strip() == "":
                        break
                    headers.append(str(header).strip())
                    col += 1

                if len(headers) < 1:
                    raise ValueError("Excel文件至少需要1列")

                logger.info(f"读取到表头: {headers}")

                # 查找"需求变更类型"列的索引
                category_col_idx = None
                description_col_idx = None

                for idx, header in enumerate(headers):
                    if "需求变更类型" in header:
                        category_col_idx = idx + 1  # Excel列索引从1开始
                        logger.info(f"找到需求变更类型列: {header} (第{category_col_idx}列)")
                        break

                # 如果没有找到"需求变更类型"列，使用第一列
                if category_col_idx is None:
                    category_col_idx = 1
                    logger.warning("未找到'需求变更类型'列，使用第1列作为分类名称")

                # 查找描述列（通常是第二列或紧跟在分类列后面的列）
                if len(headers) >= 2:
                    if category_col_idx == 1:
                        description_col_idx = 2
                    else:
                        # 如果分类列不是第一列，尝试找到描述列
                        for idx, header in enumerate(headers):
                            if "描述" in header or "说明" in header or "定义" in header:
                                description_col_idx = idx + 1
                                logger.info(f"找到描述列: {header} (第{description_col_idx}列)")
                                break

                        # 如果没找到描述列，使用分类列后面的一列
                        if description_col_idx is None and category_col_idx < len(headers):
                            description_col_idx = category_col_idx + 1
                            logger.info(f"使用第{description_col_idx}列作为描述列")

                # 读取数据，从第2行开始
                categories = []
                row = 2

                while True:
                    # 读取分类名称
                    category_name = excel.read_cell(sheet_name, row, category_col_idx)
                    if not category_name or str(category_name).strip() == "":
                        break

                    # 读取描述
                    description = ""
                    if description_col_idx:
                        description = excel.read_cell(sheet_name, row, description_col_idx)
                        description = str(description).strip() if description else ""

                    categories.append({
                        "row": row,
                        "category_name": str(category_name).strip(),
                        "description": description
                    })

                    row += 1

                logger.info(f"读取到 {len(categories)} 个分类")
                return categories

        except Exception as e:
            logger.error(f"读取Excel文件失败: {str(e)}")
            raise

    def _generate_descriptions_with_ai(self, categories: List[Dict[str, Any]]) -> List[CategoryDescription]:
        """
        使用AI生成分类描述

        Args:
            categories: 分类信息列表

        Returns:
            AI生成的分类描述列表
        """
        try:
            # 过滤出需要生成描述的分类（描述为空的）
            empty_categories = [cat for cat in categories if not cat["description"]]

            if not empty_categories:
                logger.info("所有分类都已有描述，无需AI生成")
                return []

            logger.info(f"需要为 {len(empty_categories)} 个分类生成描述")

            # 创建提示模板
            template = ChatPromptTemplate.from_messages([
                ("system", """你是一个专业的汽车软件开发质量工程师，精通汽车电子系统和软件开发流程。
你的任务是为给定的变更需求对应的分类生成专业、准确的描述。
背景： 汽车软件开发领域中，需求变更可以分为很多类型。为了更好地判断某个需求变更属于哪种类型，
需要为每种变更类型补充详细的描述。

提示：
描述应包括以下信息：
该变更类型的核心定义。
变更可能影响的功能或模块。
判断该类型变更的关键特征或依据。

要求：
1. 描述应该专业且准确，体现汽车软件开发的特点
2. 使用中文描述
3. 避免过于技术性的术语，保持可读性
4. 严格按照输入的分类顺序返回描述，确保顺序完全一致"""),
                ("user", """请为以下分类名称生成专业的描述：

{category_names}

请为每个分类生成合适的描述，并说明生成该描述的理由。
重要：请严格按照给定的顺序返回描述，确保返回的描述顺序与输入的分类顺序完全一致。""")
            ])

            # 准备输入数据
            category_names = "\n".join([f"{i + 1}. {cat['category_name']}" for i, cat in enumerate(empty_categories)])
            invoke_data = {
                "category_names": category_names
            }

            # 调用AI生成描述
            resp: CategoryDescriptionList = get_ai_message_with_structured_output(
                template,
                invoke_data,
                CategoryDescriptionList
            )

            logger.info(f"AI成功生成 {len(resp.descriptions)} 个分类描述")
            return resp.descriptions

        except Exception as e:
            logger.error(f"AI生成描述失败: {str(e)}")
            raise

    def _write_descriptions_to_excel(self, excel_path: str, categories: List[Dict[str, Any]],
                                     ai_descriptions: List[CategoryDescription]) -> int:
        """
        将AI生成的描述写入Excel文件

        Args:
            excel_path: Excel文件路径
            categories: 原始分类信息
            ai_descriptions: AI生成的描述

        Returns:
            更新的行数
        """
        try:
            # 过滤出需要更新的分类（描述为空的）
            empty_categories = [cat for cat in categories if not cat["description"]]

            # 按顺序匹配AI生成的描述
            updated_count = 0

            with ExcelUtil(excel_path, engine="openpyxl") as excel:
                sheet_names = excel.get_sheet_names()
                sheet_name = sheet_names[0]

                # 读取表头以确定描述列位置
                headers = []
                col = 1
                while True:
                    header = excel.read_cell(sheet_name, 1, col)
                    if not header or str(header).strip() == "":
                        break
                    headers.append(str(header).strip())
                    col += 1

                # 查找"需求变更类型"列和描述列的索引
                category_col_idx = None
                description_col_idx = None

                for idx, header in enumerate(headers):
                    if "需求变更类型" in header:
                        category_col_idx = idx + 1  # Excel列索引从1开始
                        break

                # 如果没有找到"需求变更类型"列，使用第一列
                if category_col_idx is None:
                    category_col_idx = 1

                # 查找描述列
                for idx, header in enumerate(headers):
                    if (idx + 1) != category_col_idx and ("描述" in header or "说明" in header or "定义" in header):
                        description_col_idx = idx + 1
                        logger.info(f"找到描述列: {header} (第{description_col_idx}列)")
                        break

                # 如果没找到描述列，使用分类列后面的一列
                if description_col_idx is None:
                    if category_col_idx == 1 and len(headers) >= 2:
                        description_col_idx = 2
                    elif category_col_idx < len(headers):
                        description_col_idx = category_col_idx + 1
                    else:
                        description_col_idx = 2  # 默认使用第2列

                    logger.info(f"使用第{description_col_idx}列作为描述列")

                # 按顺序匹配：第i个空白分类对应第i个AI生成的描述
                for i, category in enumerate(empty_categories):
                    if i < len(ai_descriptions):
                        ai_desc = ai_descriptions[i]
                        new_description = ai_desc.description

                        # 写入描述到确定的描述列
                        excel.write_cell(sheet_name, category["row"], description_col_idx, new_description)
                        updated_count += 1

                        logger.info(f"更新分类 '{category['category_name']}' 的描述: {new_description}")
                        logger.debug(
                            f"AI原始分类名: '{ai_desc.category_name}', 实际分类名: '{category['category_name']}'")

                # 保存文件
                excel.save()

            logger.info(f"成功更新 {updated_count} 个分类的描述")
            return updated_count

        except Exception as e:
            logger.error(f"写入Excel文件失败: {str(e)}")
            raise

    def enhance_rule_descriptions(self, rule_key: str, auto_enhance: bool = True) -> Dict[str, Any]:
        """
        使用AI增强规则文件中的分类描述

        Args:
            rule_key: 规则唯一标识
            auto_enhance: 是否自动增强（为True时会自动写入Excel文件）

        Returns:
            增强结果信息

        Raises:
            ValueError: 当规则不存在时
            FileNotFoundError: 当Excel文件不存在时
        """
        logger.info(f"开始AI增强规则描述: {rule_key}")

        try:
            # 获取规则信息
            rule_metadata = self.get_rule(rule_key)
            if not rule_metadata:
                raise ValueError(f"规则不存在: {rule_key}")

            # 获取Excel文件路径
            excel_path = self.get_rule_excel_path(rule_key)
            if not excel_path or not os.path.exists(excel_path):
                raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

            # 读取分类信息
            categories = self._read_excel_categories(excel_path)
            if not categories:
                return {
                    "rule_key": rule_key,
                    "message": "Excel文件中没有找到分类数据",
                    "enhanced_count": 0,
                    "total_count": 0
                }

            # 统计需要增强的分类数量
            empty_count = len([cat for cat in categories if not cat["description"]])

            if empty_count == 0:
                return {
                    "rule_key": rule_key,
                    "message": "所有分类都已有描述，无需增强",
                    "enhanced_count": 0,
                    "total_count": len(categories)
                }

            # 使用AI生成描述
            ai_descriptions = self._generate_descriptions_with_ai(categories)

            enhanced_count = 0
            if auto_enhance and ai_descriptions:
                # 自动写入Excel文件
                enhanced_count = self._write_descriptions_to_excel(excel_path, categories, ai_descriptions)

                # 更新规则的更新时间
                metadata = self._load_metadata()
                if rule_key in metadata:
                    metadata[rule_key].updated_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self._save_metadata(metadata)

            result = {
                "rule_key": rule_key,
                "message": f"AI增强完成，共处理 {len(categories)} 个分类，增强 {enhanced_count} 个描述",
                "enhanced_count": enhanced_count,
                "total_count": len(categories),
                "empty_count": empty_count,
                "ai_descriptions": [desc.model_dump() for desc in ai_descriptions] if not auto_enhance else None
            }

            logger.info(f"AI增强完成: {result['message']}")
            return result

        except Exception as e:
            logger.error(f"AI增强失败: {str(e)}")
            raise

    def preview_ai_enhancements(self, rule_key: str) -> Dict[str, Any]:
        """
        预览AI增强结果，不写入文件

        Args:
            rule_key: 规则唯一标识

        Returns:
            预览结果，包含AI生成的描述
        """
        logger.info(f"预览AI增强结果: {rule_key}")
        return self.enhance_rule_descriptions(rule_key, auto_enhance=False)

    def get_rule_categories(self, rule_key: str) -> List[CategoryContent]:
        """
        获取某个规则对应的所有分类和描述
        Args:
            rule_key: 规则唯一标识
        Returns:
            分类内容列表
        Raises:
            ValueError: 当规则不存在时
            FileNotFoundError: 当Excel文件不存在时
        """
        logger.info(f"获取规则分类: {rule_key}")

        try:
            # 获取规则信息
            rule_metadata = self.get_rule(rule_key)
            if not rule_metadata:
                raise ValueError(f"规则不存在: {rule_key}")

            # 获取Excel文件路径
            excel_path = self.get_rule_excel_path(rule_key)
            if not excel_path or not os.path.exists(excel_path):
                raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

            # 读取扩展分类信息
            categories = self._read_excel_extended_content(excel_path)

            # 转换为CategoryContent格式
            category_contents = []
            for cat in categories:
                category_content = CategoryContent(
                    category_name=cat["category_name"],
                    description=cat["description"],
                    additional_fields=cat["additional_fields"]  # 包含扩展字段
                )
                category_contents.append(category_content)

            logger.info(f"成功获取 {len(category_contents)} 个分类")
            return category_contents

        except Exception as e:
            logger.error(f"获取规则分类失败: {str(e)}")
            raise

    def get_category_content(self, rule_key: str, category_name: str) -> Optional[CategoryContent]:
        """
        获取某个规则具体分类所对应的具体规则内容

        Args:
            rule_key: 规则唯一标识
            category_name: 分类名称

        Returns:
            分类内容，如果不存在返回None

        Raises:
            ValueError: 当规则不存在时
            FileNotFoundError: 当Excel文件不存在时
        """
        logger.info(f"获取分类内容: {rule_key} - {category_name}")

        try:
            # 获取所有分类
            categories = self.get_rule_categories(rule_key)

            # 查找指定分类
            for category in categories:
                if category.category_name == category_name:
                    logger.info(f"找到分类: {category_name}")
                    return category

            logger.warning(f"未找到分类: {category_name}")
            return None

        except Exception as e:
            logger.error(f"获取分类内容失败: {str(e)}")
            raise

    def _read_excel_extended_content(self, excel_path: str) -> List[Dict[str, Any]]:
        """
        读取Excel文件中的扩展内容（包含更多字段）
        Args:
            excel_path: Excel文件路径
        Returns:
            包含所有字段的分类信息列表
        """
        try:
            with ExcelUtil(excel_path, engine="openpyxl") as excel:
                # 获取第一个工作表
                sheet_names = excel.get_sheet_names()
                if not sheet_names:
                    raise ValueError("Excel文件中没有工作表")

                sheet_name = sheet_names[0]
                logger.info(f"读取扩展内容工作表: {sheet_name}")

                # 读取表头（第1行）
                headers = []
                col = 1
                while True:
                    header = excel.read_cell(sheet_name, 1, col)
                    if not header or str(header).strip() == "":
                        break
                    headers.append(str(header).strip())
                    col += 1

                if len(headers) < 1:
                    raise ValueError("Excel文件至少需要1列")

                logger.info(f"读取到表头: {headers}")

                # 查找"需求变更类型"列的索引
                category_col_idx = None
                description_col_idx = None

                for idx, header in enumerate(headers):
                    if "需求变更类型" in header:
                        category_col_idx = idx
                        logger.info(f"找到需求变更类型列: {header} (第{idx + 1}列)")
                        break

                # 如果没有找到"需求变更类型"列，使用第一列
                if category_col_idx is None:
                    category_col_idx = 0
                    logger.warning("未找到'需求变更类型'列，使用第1列作为分类名称")

                # 查找描述列
                for idx, header in enumerate(headers):
                    if idx != category_col_idx and ("描述" in header or "说明" in header or "定义" in header):
                        description_col_idx = idx
                        logger.info(f"找到描述列: {header} (第{idx + 1}列)")
                        break

                # 如果没找到描述列，使用分类列后面的一列（如果存在）
                if description_col_idx is None and category_col_idx + 1 < len(headers):
                    description_col_idx = category_col_idx + 1
                    logger.info(f"使用第{description_col_idx + 1}列作为描述列")

                # 读取数据行
                categories = []
                row = 2

                while True:
                    # 读取分类名称
                    category_name = excel.read_cell(sheet_name, row, category_col_idx + 1)
                    if not category_name or str(category_name).strip() == "":
                        break

                    # 读取所有列的数据
                    row_data = {
                        "row": row,
                        "category_name": str(category_name).strip(),
                        "description": "",
                        "additional_fields": {}
                    }

                    for col_idx, header in enumerate(headers):
                        cell_value = excel.read_cell(sheet_name, row, col_idx + 1)
                        cell_value = str(cell_value).strip() if cell_value else ""

                        if col_idx == category_col_idx:  # 分类名称列
                            row_data["category_name"] = cell_value
                        elif col_idx == description_col_idx:  # 描述列
                            row_data["description"] = cell_value
                        else:  # 其他字段
                            row_data["additional_fields"][header] = cell_value

                    categories.append(row_data)
                    row += 1

                logger.info(f"读取到 {len(categories)} 个分类的扩展内容")
                return categories

        except Exception as e:
            logger.error(f"读取Excel扩展内容失败: {str(e)}")
            raise

    def polish_category_content(self, rule_key: str, category_name: str,
                                change_request: Union[str, Dict[str, Any]],
                                fields_to_polish: Optional[List[str]] = None) -> List[ContentPolishResult]:
        """
        基于变更需求对分类内容进行润色

        Args:
            rule_key: 规则唯一标识
            category_name: 分类名称
            change_request: 变更需求信息（字符串或JSON）
            fields_to_polish: 需要润色的字段列表，None表示润色所有字段

        Returns:
            润色结果列表

        Raises:
            ValueError: 当规则或分类不存在时
        """
        logger.info(f"润色分类内容: {rule_key} - {category_name}")

        try:
            # 获取规则信息
            rule_metadata = self.get_rule(rule_key)
            if not rule_metadata:
                raise ValueError(f"规则不存在: {rule_key}")

            # 获取Excel文件路径并读取扩展内容
            excel_path = self.get_rule_excel_path(rule_key)
            if not excel_path or not os.path.exists(excel_path):
                raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

            # 读取扩展内容
            categories = self._read_excel_extended_content(excel_path)

            # 查找指定分类
            target_category = None
            for cat in categories:
                if cat["category_name"] == category_name:
                    target_category = cat
                    break

            if not target_category:
                raise ValueError(f"分类不存在: {category_name}")

            # 准备变更需求文本
            if isinstance(change_request, dict):
                change_request_text = json.dumps(change_request, ensure_ascii=False, indent=2)
            else:
                change_request_text = str(change_request)

            # 确定需要润色的字段
            # available_fields = ["description"] + list(target_category["additional_fields"].keys())
            available_fields = list(target_category["additional_fields"].keys())
            if fields_to_polish is None:
                fields_to_polish = available_fields
            else:
                # 验证字段是否存在
                invalid_fields = [f for f in fields_to_polish if f not in available_fields]
                if invalid_fields:
                    raise ValueError(f"字段不存在: {invalid_fields}")

            # 使用AI进行润色
            polish_results = self._polish_content_with_ai(
                target_category, change_request_text, fields_to_polish
            )

            logger.info(f"成功润色 {len(polish_results)} 个字段")
            return polish_results

        except Exception as e:
            logger.error(f"润色分类内容失败: {str(e)}")
            raise

    def _polish_content_with_ai(self, category: Dict[str, Any], change_request: str,
                                fields_to_polish: List[str]) -> List[ContentPolishResult]:
        """
        使用AI润色内容

        Args:
            category: 分类信息
            change_request: 变更需求文本
            fields_to_polish: 需要润色的字段列表
            role_name: 角色名称

        Returns:
            润色结果列表
        """
        try:
            # 创建提示模板
            template = ChatPromptTemplate.from_messages([
                ("system", """你是一个专业的汽车软件开发，精通需求变更分析和文档润色。
你的任务是根据具体的变更需求信息，对现有的分类规则内容进行润色和优化。

要求：
1. 保持原有内容的核心含义和结构
2. 结合变更需求的具体情况进行针对性优化
3. 使用专业、准确、清晰的表达
4. 确保润色后的内容更加贴合实际变更场景
5. 使用中文表达"""),
                ("user", """请根据以下变更需求信息，对指定字段的内容进行润色：

变更需求信息：
{change_request}

分类名称：{category_name}

需要润色的字段和当前内容：
{fields_content}

请为每个字段提供润色后的内容，并说明润色的理由。""")
            ])

            # 准备字段内容
            fields_content_list = []
            for field in fields_to_polish:
                if field == "description":
                    current_content = category["description"]
                else:
                    current_content = category["additional_fields"].get(field, "")

                fields_content_list.append(f"- {field}: {current_content}")

            fields_content = "\n".join(fields_content_list)

            # 准备输入数据
            invoke_data = {
                "change_request": change_request,
                "category_name": category["category_name"],
                "fields_content": fields_content
            }

            # 定义响应模型
            class PolishResultList(BaseModel):
                results: List[ContentPolishResult] = Field(..., description="润色结果列表")

            # 调用AI进行润色
            resp: PolishResultList = get_ai_message_with_structured_output(
                template,
                invoke_data,
                PolishResultList
            )

            logger.info(f"AI成功润色 {len(resp.results)} 个字段")
            return resp.results

        except Exception as e:
            logger.error(f"AI润色失败: {str(e)}")
            raise

    def match_rule_by_change_request(self, change_request: Union[str, Dict[str, Any]],
                                     top_k: int = 3) -> List[RuleMatchResult]:
        """
        基于变更需求信息使用AI判断匹配的规则

        Args:
            change_request: 变更需求信息（字符串或JSON）
            top_k: 返回前k个匹配结果

        Returns:
            匹配的规则列表，按置信度降序排列
        """
        logger.info(f"AI匹配规则: top_k={top_k}")

        try:
            # 获取所有规则
            all_rules = self.list_rules(page=1, page_size=1000)  # 获取所有规则
            if not all_rules["rules"]:
                logger.warning("没有可用的规则进行匹配")
                return []

            # 准备变更需求文本
            if isinstance(change_request, dict):
                change_request_text = json.dumps(change_request, ensure_ascii=False, indent=2)
            else:
                change_request_text = str(change_request)

            # 使用AI进行规则匹配
            match_results = self._match_rules_with_ai(change_request_text, all_rules["rules"], top_k)

            logger.info(f"AI匹配到 {len(match_results)} 个规则")
            return match_results

        except Exception as e:
            logger.error(f"AI匹配规则失败: {str(e)}")
            raise

    def _match_single_rule_with_ai(self, change_request: str, rule: Dict[str, Any]) -> RuleMatchResult:
        """
        使用AI匹配单个规则

        Args:
            change_request: 变更需求文本
            rule: 单个规则信息

        Returns:
            匹配结果
        """
        try:
            # 创建提示模板
            template = ChatPromptTemplate.from_messages([
                ("system", """你是一个专业的汽车软件开发专家，精通需求变更分析和规则匹配。
你的任务是根据具体的变更需求信息，评估给定规则与变更需求的匹配程度。

要求：
1. 分析变更需求的核心内容和特征
2. 评估规则与变更需求的匹配程度
3. 给出置信度评分（0-1之间，1表示完全匹配）
4. 提供详细的匹配理由"""),
                ("user", """请根据以下变更需求信息，评估规则的匹配程度：

变更需求信息：
{change_request}

规则信息：
- 规则key: {rule_key}
- 规则名称: {rule_name}
- 规则描述: {rule_description}
- 角色: {role_name}

请分析该规则与变更需求的匹配程度，给出置信度评分和详细理由。""")
            ])

            # 准备输入数据
            invoke_data = {
                "change_request": change_request,
                "rule_key": rule['rule_key'],
                "rule_name": rule['rule_name'],
                "rule_description": rule['rule_description'],
                "role_name": rule['role_name']
            }

            # 调用AI进行匹配
            resp: SingleRuleMatchResponse = get_ai_message_with_structured_output(
                template,
                invoke_data,
                SingleRuleMatchResponse
            )

            # 转换为RuleMatchResult
            return RuleMatchResult(
                rule_key=resp.rule_key,
                rule_name=resp.rule_name,
                confidence=resp.confidence,
                reasoning=resp.reasoning
            )

        except Exception as e:
            logger.error(f"AI匹配单个规则失败: {str(e)}")
            raise

    def _match_rules_with_ai(self, change_request: str, rules: List[Dict[str, Any]],
                             top_k: int) -> List[RuleMatchResult]:
        """
        使用AI匹配规则（逐个分析）

        Args:
            change_request: 变更需求文本
            rules: 规则列表
            top_k: 返回前k个结果

        Returns:
            匹配结果列表
        """
        try:
            logger.info(f"开始逐个分析 {len(rules)} 个规则")
            match_results = []

            # 逐个分析每个规则
            for i, rule in enumerate(rules):
                logger.info(f"分析规则 {i + 1}/{len(rules)}: {rule['rule_key']}")
                try:
                    result = self._match_single_rule_with_ai(change_request, rule)
                    # 防止大模型返回名称有偏差
                    result.rule_key = rule['rule_key']
                    result.rule_name = rule['rule_name']
                    match_results.append(result)
                    logger.info(f"规则 {rule['rule_key']} 匹配置信度: {result.confidence}")
                except Exception as e:
                    logger.warning(f"分析规则 {rule['rule_key']} 失败: {str(e)}")
                    # 创建一个默认的低置信度结果
                    match_results.append(RuleMatchResult(
                        rule_key=rule['rule_key'],
                        rule_name=rule['rule_name'],
                        confidence=0.0,
                        reasoning=f"分析失败: {str(e)}"
                    ))

            # 按置信度排序并限制数量
            sorted_results = sorted(match_results, key=lambda x: x.confidence, reverse=True)
            logger.info(f"规则匹配完成，返回前 {top_k} 个结果")
            return sorted_results[:top_k]

        except Exception as e:
            logger.error(f"AI匹配规则失败: {str(e)}")
            raise

    def match_category_by_change_request(self, rule_key: str, change_request: Union[str, Dict[str, Any]],
                                         top_k: int = 3) -> tuple[List[CategoryMatchResult], List[CategoryContent]]:
        """
        基于变更需求信息使用AI判断匹配的分类

        Args:
            rule_key: 规则唯一标识
            change_request: 变更需求信息（字符串或JSON）
            top_k: 返回前k个匹配结果

        Returns:
            匹配的分类列表，按置信度降序排列

        Raises:
            ValueError: 当规则不存在时
        """
        logger.info(f"AI匹配分类: {rule_key}, top_k={top_k}")

        try:
            # 获取规则的所有分类
            categories = self.get_rule_categories(rule_key)
            if not categories:
                logger.warning(f"规则 {rule_key} 没有可用的分类")
                return []

            # 准备变更需求文本
            if isinstance(change_request, dict):
                change_request_text = json.dumps(change_request, ensure_ascii=False, indent=2)
            else:
                change_request_text = str(change_request)

            # 使用AI进行分类匹配
            match_results = self._match_categories_with_ai(change_request_text, categories, top_k)

            logger.info(f"AI匹配到 {len(match_results)} 个分类")
            return match_results, categories

        except Exception as e:
            logger.error(f"AI匹配分类失败: {str(e)}")
            raise

    def _match_single_category_with_ai(self, change_request: str, category: CategoryContent) -> CategoryMatchResult:
        """
        使用AI匹配单个分类

        Args:
            change_request: 变更需求文本
            category: 单个分类信息

        Returns:
            匹配结果
        """
        try:
            # 创建提示模板
            template = ChatPromptTemplate.from_messages([
                ("system", """你是一个专业的汽车软件开发专家，精通需求变更分析和分类匹配。
你的任务是根据具体的变更需求信息，评估给定分类与变更需求的匹配程度。

要求：
1. 分析变更需求的核心内容和特征
2. 评估分类与变更需求的匹配程度
3. 给出置信度评分（0-1之间，1表示完全匹配）
4. 提供详细的匹配理由"""),
                ("user", """请根据以下变更需求信息，评估分类的匹配程度：

变更需求信息：
{change_request}

分类信息：
- 分类名称: {category_name}
- 分类描述: {description}
{additional_info}

请分析该分类与变更需求的匹配程度，给出置信度评分和详细理由。""")
            ])

            # 准备额外信息
            additional_info = ""
            if category.additional_fields:
                additional_info = f"- 其他信息: {category.additional_fields}"

            # 准备输入数据
            invoke_data = {
                "change_request": change_request,
                "category_name": category.category_name,
                "description": category.description,
                "additional_info": additional_info
            }

            # 调用AI进行匹配
            resp: SingleCategoryMatchResponse = get_ai_message_with_structured_output(
                template,
                invoke_data,
                SingleCategoryMatchResponse
            )

            # 转换为CategoryMatchResult
            return CategoryMatchResult(
                category_name=resp.category_name,
                confidence=resp.confidence,
                reasoning=resp.reasoning
            )

        except Exception as e:
            logger.error(f"AI匹配单个分类失败: {str(e)}")
            raise

    def _match_categories_with_ai(self, change_request: str, categories: List[CategoryContent],
                                  top_k: int) -> List[CategoryMatchResult]:
        """
        使用AI匹配分类（逐个分析）

        Args:
            change_request: 变更需求文本
            categories: 分类列表
            top_k: 返回前k个结果

        Returns:
            匹配结果列表
        """
        try:
            logger.info(f"开始逐个分析 {len(categories)} 个分类")
            match_results = []

            # 逐个分析每个分类
            for i, category in enumerate(categories):
                logger.info(f"分析分类 {i + 1}/{len(categories)}: {category.category_name}")
                try:
                    result = self._match_single_category_with_ai(change_request, category)
                    # 防止大模型类型名称返回存在区别
                    result.category_name = category.category_name
                    match_results.append(result)
                    logger.info(f"分类 {category.category_name} 匹配置信度: {result.confidence}")
                except Exception as e:
                    logger.warning(f"分析分类 {category.category_name} 失败: {str(e)}")
                    # 创建一个默认的低置信度结果
                    match_results.append(CategoryMatchResult(
                        category_name=category.category_name,
                        confidence=0.0,
                        reasoning=f"分析失败: {str(e)}"
                    ))

            # 按置信度排序并限制数量
            sorted_results = sorted(match_results, key=lambda x: x.confidence, reverse=True)
            logger.info(f"分类匹配完成，返回前 {top_k} 个结果")
            return sorted_results[:top_k]

        except Exception as e:
            logger.error(f"AI匹配分类失败: {str(e)}")
            raise

    def get_category_additional_fields(self, rule_key: str, category_name: str) -> Dict[str, str]:
        """
        获取规则中特定分类的额外属性

        Args:
            rule_key: 规则唯一标识
            category_name: 分类名称

        Returns:
            额外属性字典，键为属性名，值为属性值

        Raises:
            ValueError: 当规则或分类不存在时
            FileNotFoundError: 当Excel文件不存在时
        """
        logger.info(f"获取分类额外属性: {rule_key} - {category_name}")

        try:
            # 获取分类内容
            category_content = self.get_category_content(rule_key, category_name)
            if not category_content:
                raise ValueError(f"分类不存在: {category_name}")

            # 返回额外属性
            additional_fields = category_content.additional_fields
            logger.info(f"成功获取 {len(additional_fields)} 个额外属性")

            return additional_fields

        except Exception as e:
            logger.error(f"获取分类额外属性失败: {str(e)}")
            raise

    def get_category_specific_field(self, rule_key: str, category_name: str, field_name: str) -> Optional[str]:
        """
        获取规则中特定分类的指定额外属性值

        Args:
            rule_key: 规则唯一标识
            category_name: 分类名称
            field_name: 属性名称

        Returns:
            属性值，如果不存在返回None

        Raises:
            ValueError: 当规则或分类不存在时
            FileNotFoundError: 当Excel文件不存在时
        """
        logger.info(f"获取分类指定属性: {rule_key} - {category_name} - {field_name}")

        try:
            # 获取额外属性
            additional_fields = self.get_category_additional_fields(rule_key, category_name)

            # 获取指定属性值
            field_value = additional_fields.get(field_name)

            if field_value:
                logger.info(f"成功获取属性值: {field_name} = {field_value}")
            else:
                logger.warning(f"属性不存在或为空: {field_name}")

            return field_value

        except Exception as e:
            logger.error(f"获取分类指定属性失败: {str(e)}")
            raise

    def list_category_field_names(self, rule_key: str, category_name: str) -> List[str]:
        """
        列出规则中特定分类的所有额外属性名称

        Args:
            rule_key: 规则唯一标识
            category_name: 分类名称

        Returns:
            属性名称列表

        Raises:
            ValueError: 当规则或分类不存在时
            FileNotFoundError: 当Excel文件不存在时
        """
        logger.info(f"列出分类属性名称: {rule_key} - {category_name}")

        try:
            # 获取额外属性
            additional_fields = self.get_category_additional_fields(rule_key, category_name)

            # 获取属性名称列表
            field_names = list(additional_fields.keys())
            logger.info(f"成功获取 {len(field_names)} 个属性名称: {field_names}")

            return field_names

        except Exception as e:
            logger.error(f"列出分类属性名称失败: {str(e)}")
            raise


if __name__ == '__main__':
    manager = GuidelineManager()
    res = manager.get_rule_categories('CSTM')
    pass
