#!/usr/bin/env python3
"""
简单测试：将 Draw.io 图像插入到 Excel 的固定单元格
"""

from pathlib import Path
from sdw_agent.util.drawio_util.drawio_to_excel_final import drawio_to_excel


def test_single_cell():
    """测试单个固定单元格位置"""
    print("🎯 测试固定单元格位置")
    print("=" * 40)
    
    # 清理输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 测试插入到 B5 单元格
    result = drawio_to_excel(
        excel_file="output/test_B5_cell.xlsx",
        sheet_name="测试工作表",
        title="架构图 - B5位置",
        image_row=5,        # 第5行
        image_col=2,        # 第2列 (B列)
        image_width=600,    # 固定宽度
        image_height=400    # 固定高度
    )
    
    print(f"结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
    print(f"消息: {result['message']}")
    
    if result['success']:
        print(f"📊 Excel文件: {result['excel_file']}")
        print(f"📍 图像位置: {result['image_position']}")
        print(f"📸 PNG文件: {result['png_file']}")
    elif 'png_file' in result:
        print(f"💡 PNG文件已生成: {result['png_file']}")
    
    return result['success']


def test_column_letter():
    """测试使用列字母"""
    print("\n🎯 测试使用列字母")
    print("=" * 40)
    
    result = drawio_to_excel(
        excel_file="output/test_D3_cell.xlsx",
        sheet_name="列字母测试",
        title="架构图 - D3位置",
        image_row=3,        # 第3行
        image_col="D",      # D列
        image_width=800,
        image_height=500
    )
    
    print(f"结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
    print(f"消息: {result['message']}")
    
    if result['success']:
        print(f"📊 Excel文件: {result['excel_file']}")
        print(f"📍 图像位置: {result['image_position']}")
    
    return result['success']


def test_no_title():
    """测试不添加标题，直接插入图像"""
    print("\n🎯 测试无标题直接插入")
    print("=" * 40)
    
    result = drawio_to_excel(
        excel_file="output/test_no_title.xlsx",
        sheet_name="无标题测试",
        title="",           # 空标题
        image_row=1,        # 第1行
        image_col=5,        # 第1列 (A1)
        image_width=600,
        image_height=400
    )
    
    print(f"结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
    print(f"消息: {result['message']}")
    
    if result['success']:
        print(f"📊 Excel文件: {result['excel_file']}")
        print(f"📍 图像位置: {result['image_position']}")
    
    return result['success']


def main():
    """主测试函数"""
    print("🚀 Draw.io 固定单元格插入测试")
    print("=" * 50)
    
    results = []
    
    # 运行测试
    try:
        # results.append(test_single_cell())
        # results.append(test_column_letter())
        results.append(test_no_title())
        
        # 统计结果
        success_count = sum(results)
        total_count = len(results)
        
        print(f"\n📊 测试结果统计:")
        print(f"   成功: {success_count}/{total_count}")
        print(f"   成功率: {success_count/total_count*100:.1f}%")
        
        if success_count > 0:
            print(f"\n🎉 测试完成！请查看 output 目录中的 Excel 文件")
            print(f"💡 每个 Excel 文件都包含插入到不同位置的 Draw.io 图表")
        else:
            print(f"\n❌ 所有测试都失败了")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")


if __name__ == "__main__":
    main()
