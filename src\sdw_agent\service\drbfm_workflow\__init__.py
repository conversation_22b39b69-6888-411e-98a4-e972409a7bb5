"""
DRBFM工作流模块

DRBFM (Design Review Based on Failure Mode) 工作流

基于故障模式的设计评审工作流，包含7个步骤：
1. 要件和SCL分析
2. 变更概要写入DRBFM
3. Block图生成并写入DRBFM
4. 变更点、变化点比较作成，写入DRBFM
5. 担心点抽出表作成，写入DRBFM
6. FTA作成，写入DRBFM
7. DRBFM sheet页作成
"""

from .drbfm_workflow import DrbfmWorkflow
from .models import (
    # 请求模型
    DrbfmAnalysisRequest,
    DrbfmSummaryRequest,
    DrbfmBlockDiagramRequest,
    DrbfmChangePointRequest,
    DrbfmConcernRequest,
    DrbfmFtaRequest,
    DrbfmSheetRequest,
    
    # 输出数据模型
    DrbfmAnalysisOutputData,
    DrbfmSummaryOutputData,
    DrbfmBlockDiagramOutputData,
    DrbfmChangePointOutputData,
    DrbfmConcernOutputData,
    DrbfmFtaOutputData,
    DrbfmSheetOutputData,
    
    # 配置模型
    DrbfmWorkflowConfigModel
)

__all__ = [
    # 工作流类
    "DrbfmWorkflow",
    
    # 请求模型
    "DrbfmAnalysisRequest",
    "DrbfmSummaryRequest", 
    "DrbfmBlockDiagramRequest",
    "DrbfmChangePointRequest",
    "DrbfmConcernRequest",
    "DrbfmFtaRequest",
    "DrbfmSheetRequest",
    
    # 输出数据模型
    "DrbfmAnalysisOutputData",
    "DrbfmSummaryOutputData",
    "DrbfmBlockDiagramOutputData",
    "DrbfmChangePointOutputData",
    "DrbfmConcernOutputData",
    "DrbfmFtaOutputData",
    "DrbfmSheetOutputData",
    
    # 配置模型
    "DrbfmWorkflowConfigModel"
]
