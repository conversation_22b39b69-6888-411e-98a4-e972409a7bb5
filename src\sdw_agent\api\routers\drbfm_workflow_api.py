"""
DRBFM工作流 API

V字对应：
DRBFM (Design Review Based on Failure Mode) 工作流

DRBFM工作流包含7个步骤：
1. 要件和SCL分析
2. 变更概要写入DRBFM
3. Block图生成并写入DRBFM
4. 变更点、变化点比较作成，写入DRBFM
5. 担心点抽出表作成，写入DRBFM
6. FTA作成，写入DRBFM
7. DRBFM sheet页作成

优化特性：
1. 增强错误处理和异常管理
2. 改进类型安全和数据验证
3. 优化代码结构和可维护性
4. 增强日志记录
5. 添加API文档
6. 工作流架构设计
"""
from typing import Any

from fastapi import APIRouter, HTTPException, status
from loguru import logger
from pydantic import BaseModel

from sdw_agent.service.drbfm_workflow import (
    DrbfmWorkflow,
    DrbfmAnalysisRequest,
    DrbfmSummaryRequest,
    DrbfmBlockDiagramRequest,
    DrbfmChangePointRequest,
    DrbfmConcernRequest,
    DrbfmFtaRequest,
    DrbfmSheetRequest
)
from sdw_agent.service.drbfm_workflow.drbfm_workflow import DrbfmWorkflowManager

# 创建路由器
router = APIRouter(prefix="/api/sdw/drbfm", tags=["DRBFM工作流"])


# API响应模型
class DrbfmResponse(BaseModel):
    """DRBFM响应模型"""
    code: int = 0
    msg: str = ""
    data: Any = None


# API端点
@router.post(
    "/analysis",
    summary="要件和SCL分析",
    description="执行DRBFM工作流的第一步：要件和SCL分析",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功返回分析结果"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def analysis_requirements_and_scl(request: DrbfmAnalysisRequest):
    """
    要件和SCL分析

    执行DRBFM工作流的第一步：分析要件和SCL
    """
    try:
        # 获取全局工作流实例
        workflow_manager = DrbfmWorkflowManager.get_instance()
        workflow = workflow_manager.get_workflow()

        # 执行要件和SCL分析工作流
        result = workflow.execute_analysis(request)

        if result.status.value == "成功":
            logger.info(f"成功完成要件和SCL分析: {request.RequirementSource.uri}")
            return DrbfmResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"要件和SCL分析失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"要件和SCL分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"要件和SCL分析失败: {str(e)}"
        )


@router.post(
    "/summary",
    summary="变更概要写入DRBFM",
    description="执行DRBFM工作流的第二步：变更概要写入DRBFM（可选参数，如不提供则使用默认值）",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功写入变更概要"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def write_change_summary(request: DrbfmSummaryRequest = None):
    """
    变更概要写入DRBFM

    执行DRBFM工作流的第二步：将变更概要写入DRBFM文档
    如果不提供request参数，将使用默认的变更概要信息
    """
    try:
        # 获取全局工作流实例
        workflow_manager = DrbfmWorkflowManager.get_instance()
        workflow = workflow_manager.get_workflow()

        # 执行变更概要写入工作流
        result = workflow.execute_summary(request)

        if result.status.value == "成功":
            drbfm_path = workflow.workflow_state.get("drbfm_file_path", "未知路径")
            logger.info(f"成功写入变更概要: {drbfm_path}")
            return DrbfmResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"变更概要写入失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"变更概要写入失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"变更概要写入失败: {str(e)}"
        )


@router.post(
    "/block_diagram",
    summary="Block图生成并写入DRBFM",
    description="执行DRBFM工作流的第三步：Block图生成并写入DRBFM",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功生成并写入Block图"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def generate_block_diagram(request: DrbfmBlockDiagramRequest):
    """
    Block图生成并写入DRBFM
    
    执行DRBFM工作流的第三步：生成Block图并写入DRBFM文档
    """
    try:
        # 创建工作流实例
        workflow = DrbfmWorkflow()
        
        # 执行Block图生成工作流
        result = workflow.execute_block_diagram(request)
        
        if result.status.value == "成功":
            logger.info(f"成功生成并写入Block图: {request.drbfm_file_path}")
            return DrbfmResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"Block图生成失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )
    
    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Block图生成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Block图生成失败: {str(e)}"
        )


@router.post(
    "/change_point",
    summary="变更点、变化点比较作成，写入DRBFM",
    description="执行DRBFM工作流的第四步：变更点、变化点比较作成，写入DRBFM",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功作成变更点、变化点比较"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def create_change_point_comparison(request: DrbfmChangePointRequest):
    """
    变更点、变化点比较作成，写入DRBFM

    执行DRBFM工作流的第四步：作成变更点、变化点比较并写入DRBFM文档
    """
    try:
        # 创建工作流实例
        workflow = DrbfmWorkflow()

        # 执行变更点、变化点比较工作流
        result = workflow.execute_change_point(request)

        if result.status.value == "成功":
            logger.info(f"成功作成变更点、变化点比较: {request.drbfm_file_path}")
            return DrbfmResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"变更点、变化点比较作成失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"变更点、变化点比较作成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"变更点、变化点比较作成失败: {str(e)}"
        )


@router.post(
    "/concern",
    summary="担心点抽出表作成，写入DRBFM",
    description="执行DRBFM工作流的第五步：担心点抽出表作成，写入DRBFM",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功作成担心点抽出表"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def create_concern_extraction(request: DrbfmConcernRequest):
    """
    担心点抽出表作成，写入DRBFM

    执行DRBFM工作流的第五步：作成担心点抽出表并写入DRBFM文档
    """
    try:
        # 创建工作流实例
        workflow = DrbfmWorkflow()

        # 执行担心点抽出表作成工作流
        result = workflow.execute_concern(request)

        if result.status.value == "成功":
            logger.info(f"成功作成担心点抽出表: {request.drbfm_file_path}")
            return DrbfmResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"担心点抽出表作成失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"担心点抽出表作成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"担心点抽出表作成失败: {str(e)}"
        )


@router.post(
    "/fta",
    summary="FTA作成，写入DRBFM",
    description="执行DRBFM工作流的第六步：FTA作成，写入DRBFM",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功作成FTA"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def create_fta(request: DrbfmFtaRequest):
    """
    FTA作成，写入DRBFM

    执行DRBFM工作流的第六步：作成FTA并写入DRBFM文档
    """
    try:
        # 创建工作流实例
        workflow = DrbfmWorkflow()

        # 执行FTA作成工作流
        result = workflow.execute_fta(request)

        if result.status.value == "成功":
            logger.info(f"成功作成FTA: {request.drbfm_file_path}")
            return DrbfmResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"FTA作成失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"FTA作成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"FTA作成失败: {str(e)}"
        )


@router.post(
    "/sheet",
    summary="DRBFM sheet页作成",
    description="执行DRBFM工作流的第七步：DRBFM sheet页作成",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功作成DRBFM sheet页"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def create_drbfm_sheet(request: DrbfmSheetRequest):
    """
    DRBFM sheet页作成

    执行DRBFM工作流的第七步：作成DRBFM sheet页
    """
    try:
        # 创建工作流实例
        workflow = DrbfmWorkflow()

        # 执行DRBFM sheet页作成工作流
        result = workflow.execute_sheet(request)

        if result.status.value == "成功":
            logger.info(f"成功作成DRBFM sheet页: {request.drbfm_file_path}")
            return DrbfmResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"DRBFM sheet页作成失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"DRBFM sheet页作成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"DRBFM sheet页作成失败: {str(e)}"
        )


@router.get(
    "/progress",
    summary="获取工作流进度",
    description="获取当前DRBFM工作流的执行进度",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功返回工作流进度"},
        500: {"description": "服务器内部错误"}
    }
)
async def get_workflow_progress():
    """
    获取工作流进度

    返回当前DRBFM工作流的执行进度和状态信息
    """
    try:
        # 获取全局工作流实例
        workflow_manager = DrbfmWorkflowManager.get_instance()
        workflow = workflow_manager.get_workflow()

        progress = workflow.get_workflow_progress()

        return DrbfmResponse(
            code=0,
            msg="成功获取工作流进度",
            data={
                "progress": progress,
                "workflow_state": {
                    "requirement_source": workflow.workflow_state.get("requirement_source"),
                    "drbfm_file_path": workflow.workflow_state.get("drbfm_file_path"),
                    "has_analysis_result": workflow.workflow_state.get("analysis_result") is not None,
                    "has_change_summary": workflow.workflow_state.get("change_summary") is not None,
                    "has_block_diagram": workflow.workflow_state.get("block_diagram_path") is not None
                }
            }
        )

    except Exception as e:
        logger.error(f"获取工作流进度失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流进度失败: {str(e)}"
        )


@router.post(
    "/reset",
    summary="重置工作流",
    description="重置DRBFM工作流状态，清除所有中间数据",
    response_model=DrbfmResponse,
    responses={
        200: {"description": "成功重置工作流"},
        500: {"description": "服务器内部错误"}
    }
)
async def reset_workflow():
    """
    重置工作流

    清除当前DRBFM工作流的所有状态和中间数据，重新开始
    """
    try:
        # 获取全局工作流实例
        workflow_manager = DrbfmWorkflowManager.get_instance()
        workflow_manager.reset_workflow()

        logger.info("DRBFM工作流已重置")

        return DrbfmResponse(
            code=0,
            msg="成功重置工作流",
            data={"status": "reset_completed"}
        )

    except Exception as e:
        logger.error(f"重置工作流失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置工作流失败: {str(e)}"
        )
